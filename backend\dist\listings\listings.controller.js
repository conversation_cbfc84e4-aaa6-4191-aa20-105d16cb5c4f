'use strict'
var __decorate =
	(this && this.__decorate) ||
	function (decorators, target, key, desc) {
		var c = arguments.length,
			r =
				c < 3
					? target
					: desc === null
						? (desc = Object.getOwnPropertyDescriptor(target, key))
						: desc,
			d
		if (typeof Reflect === 'object' && typeof Reflect.decorate === 'function')
			r = Reflect.decorate(decorators, target, key, desc)
		else
			for (var i = decorators.length - 1; i >= 0; i--)
				if ((d = decorators[i]))
					r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r
		return (c > 3 && r && Object.defineProperty(target, key, r), r)
	}
var __metadata =
	(this && this.__metadata) ||
	function (k, v) {
		if (typeof Reflect === 'object' && typeof Reflect.metadata === 'function')
			return Reflect.metadata(k, v)
	}
var __param =
	(this && this.__param) ||
	function (paramIndex, decorator) {
		return function (target, key) {
			decorator(target, key, paramIndex)
		}
	}
Object.defineProperty(exports, '__esModule', { value: true })
exports.ListingsController = void 0
const common_1 = require('@nestjs/common')
const shared_1 = require('@mrh/shared')
const prisma_service_1 = require('../prisma/prisma.service')
const jwt_auth_guard_1 = require('../auth/jwt-auth.guard')
const policy_guard_1 = require('../auth/policy.guard')
const create_listing_dto_1 = require('./dto/create-listing.dto')
let ListingsController = class ListingsController {
	prisma
	constructor(prisma) {
		this.prisma = prisma
	}
	async create(dto, req) {
		const user = req.user
		return await (0, shared_1.makeCreateListing)({ prisma: this.prisma })(
			user,
			dto
		)
	}
}
exports.ListingsController = ListingsController
__decorate(
	[
		(0, common_1.Post)(),
		(0, common_1.UseGuards)(
			jwt_auth_guard_1.JwtAuthGuard,
			policy_guard_1.PolicyGuard
		),
		__param(0, (0, common_1.Body)()),
		__param(1, (0, common_1.Request)()),
		__metadata('design:type', Function),
		__metadata('design:paramtypes', [
			create_listing_dto_1.CreateListingDto,
			Object
		]),
		__metadata('design:returntype', Promise)
	],
	ListingsController.prototype,
	'create',
	null
)
exports.ListingsController = ListingsController = __decorate(
	[
		(0, common_1.Controller)('listings'),
		__metadata('design:paramtypes', [prisma_service_1.PrismaService])
	],
	ListingsController
)
//# sourceMappingURL=listings.controller.js.map
