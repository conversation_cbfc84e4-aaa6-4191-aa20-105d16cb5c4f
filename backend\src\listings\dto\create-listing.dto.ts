import { IsString, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Pos<PERSON>, MinLength } from 'class-validator'
import { Transform, Type } from 'class-transformer'

export class CreateListingDto {
	@IsString()
	@MinLength(1, { message: 'orgId cannot be empty' })
	orgId!: string

	@IsString()
	@MinLength(3, { message: 'title must be at least 3 characters long' })
	title!: string

	@IsNumber({}, { message: 'price must be a number' })
	@IsPositive({ message: 'price must be positive' })
	@Type(() => Number)
	@Transform(({ value }: { value: any }) => parseInt(value))
	price!: number
}
