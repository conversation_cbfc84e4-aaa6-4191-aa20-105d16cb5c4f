'use strict'
var __decorate =
	(this && this.__decorate) ||
	function (decorators, target, key, desc) {
		var c = arguments.length,
			r =
				c < 3
					? target
					: desc === null
						? (desc = Object.getOwnPropertyDescriptor(target, key))
						: desc,
			d
		if (typeof Reflect === 'object' && typeof Reflect.decorate === 'function')
			r = Reflect.decorate(decorators, target, key, desc)
		else
			for (var i = decorators.length - 1; i >= 0; i--)
				if ((d = decorators[i]))
					r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r
		return (c > 3 && r && Object.defineProperty(target, key, r), r)
	}
var __metadata =
	(this && this.__metadata) ||
	function (k, v) {
		if (typeof Reflect === 'object' && typeof Reflect.metadata === 'function')
			return Reflect.metadata(k, v)
	}
Object.defineProperty(exports, '__esModule', { value: true })
exports.JwtAuthGuard = void 0
const common_1 = require('@nestjs/common')
const jwt_1 = require('@nestjs/jwt')
const prisma_service_1 = require('../prisma/prisma.service')
let JwtAuthGuard = class JwtAuthGuard {
	jwt
	prisma
	constructor(jwt, prisma) {
		this.jwt = jwt
		this.prisma = prisma
	}
	async canActivate(ctx) {
		const req = ctx.switchToHttp().getRequest()
		const token = this.extractBearer(req.headers['authorization'])
		if (!token) throw new common_1.UnauthorizedException('Missing bearer token')
		const payload = await this.verify(token)
		const userId = payload.sub
		if (!userId)
			throw new common_1.UnauthorizedException('Invalid token payload')
		const memberships = await this.prisma.membership.findMany({
			where: { userId },
			select: { orgId: true, role: true }
		})
		const subject = { id: userId, memberships }
		req.user = subject
		return true
	}
	extractBearer(auth) {
		if (!auth) return null
		const [type, value] = auth.split(' ')
		return type?.toLowerCase() === 'bearer' && value ? value : null
	}
	async verify(token) {
		try {
			return await this.jwt.verifyAsync(token, {
				secret: process.env.JWT_SECRET,
				issuer: process.env.JWT_ISSUER,
				audience: process.env.JWT_AUDIENCE
			})
		} catch {
			throw new common_1.UnauthorizedException('Invalid or expired token')
		}
	}
}
exports.JwtAuthGuard = JwtAuthGuard
exports.JwtAuthGuard = JwtAuthGuard = __decorate(
	[
		(0, common_1.Injectable)(),
		__metadata('design:paramtypes', [
			jwt_1.JwtService,
			prisma_service_1.PrismaService
		])
	],
	JwtAuthGuard
)
//# sourceMappingURL=jwt-auth.guard.js.map
