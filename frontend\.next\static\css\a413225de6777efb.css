@font-face {
  font-family: Geist;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/8d697b304b401681-s.woff2) format('woff2');
  unicode-range: u+0301, u+0400-045f, u+0490-0491, u+04b0-04b1, u+2116;
}
@font-face {
  font-family: Geist;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/ba015fad6dcf6784-s.woff2) format('woff2');
  unicode-range:
    u+0100-02ba, u+02bd-02c5, u+02c7-02cc, u+02ce-02d7, u+02dd-02ff, u+0304,
    u+0308, u+0329, u+1d00-1dbf, u+1e00-1e9f, u+1ef2-1eff, u+2020, u+20a0-20ab,
    u+20ad-20c0, u+2113, u+2c60-2c7f, u+a720-a7ff;
}
@font-face {
  font-family: Geist;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/569ce4b8f30dc480-s.p.woff2) format('woff2');
  unicode-range:
    u+00??, u+0131, u+0152-0153, u+02bb-02bc, u+02c6, u+02da, u+02dc, u+0304,
    u+0308, u+0329, u+2000-206f, u+20ac, u+2122, u+2191, u+2193, u+2212, u+2215,
    u+feff, u+fffd;
}
@font-face {
  font-family: Geist Fallback;
  src: local('Arial');
  ascent-override: 95.94%;
  descent-override: 28.16%;
  line-gap-override: 0%;
  size-adjust: 104.76%;
}
.__className_5cfdac {
  font-family:
    Geist,
    Geist Fallback;
  font-style: normal;
}
.__variable_5cfdac {
  --font-geist-sans: 'Geist', 'Geist Fallback';
}
@font-face {
  font-family: Geist Mono;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/9610d9e46709d722-s.woff2) format('woff2');
  unicode-range: u+0301, u+0400-045f, u+0490-0491, u+04b0-04b1, u+2116;
}
@font-face {
  font-family: Geist Mono;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/747892c23ea88013-s.woff2) format('woff2');
  unicode-range:
    u+0100-02ba, u+02bd-02c5, u+02c7-02cc, u+02ce-02d7, u+02dd-02ff, u+0304,
    u+0308, u+0329, u+1d00-1dbf, u+1e00-1e9f, u+1ef2-1eff, u+2020, u+20a0-20ab,
    u+20ad-20c0, u+2113, u+2c60-2c7f, u+a720-a7ff;
}
@font-face {
  font-family: Geist Mono;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/93f479601ee12b01-s.p.woff2) format('woff2');
  unicode-range:
    u+00??, u+0131, u+0152-0153, u+02bb-02bc, u+02c6, u+02da, u+02dc, u+0304,
    u+0308, u+0329, u+2000-206f, u+20ac, u+2122, u+2191, u+2193, u+2212, u+2215,
    u+feff, u+fffd;
}
@font-face {
  font-family: Geist Mono Fallback;
  src: local('Arial');
  ascent-override: 74.67%;
  descent-override: 21.92%;
  line-gap-override: 0%;
  size-adjust: 134.59%;
}
.__className_9a8899 {
  font-family:
    Geist Mono,
    Geist Mono Fallback;
  font-style: normal;
}
.__variable_9a8899 {
  --font-geist-mono: 'Geist Mono', 'Geist Mono Fallback';
}

/*! tailwindcss v4.1.11 | MIT License | https://tailwindcss.com */
@layer properties {
  @supports (((-webkit-hyphens: none)) and (not (margin-trim: inline))) or
    ((-moz-orient: inline) and (not (color: rgb(from red r g b)))) {
    *,
    ::backdrop,
    :after,
    :before {
      --tw-border-style: solid;
      --tw-font-weight: initial;
      --tw-tracking: initial;
      --tw-blur: initial;
      --tw-brightness: initial;
      --tw-contrast: initial;
      --tw-grayscale: initial;
      --tw-hue-rotate: initial;
      --tw-invert: initial;
      --tw-opacity: initial;
      --tw-saturate: initial;
      --tw-sepia: initial;
      --tw-drop-shadow: initial;
      --tw-drop-shadow-color: initial;
      --tw-drop-shadow-alpha: 100%;
      --tw-drop-shadow-size: initial;
    }
  }
}
@layer theme {
  :host,
  :root {
    --color-black: #000;
    --color-white: #fff;
    --spacing: 0.25rem;
    --text-sm: 0.875rem;
    --text-sm--line-height: calc(1.25 / 0.875);
    --text-base: 1rem;
    --text-base--line-height: calc(1.5 / 1);
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --default-transition-duration: 0.15s;
    --default-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    --default-font-family: var(--font-geist-sans);
    --default-mono-font-family: var(--font-geist-mono);
  }
}
@layer base {
  *,
  ::backdrop,
  :after,
  :before {
    box-sizing: border-box;
    border: 0 solid;
    margin: 0;
    padding: 0;
  }
  ::file-selector-button {
    box-sizing: border-box;
    border: 0 solid;
    margin: 0;
    padding: 0;
  }
  :host,
  html {
    -webkit-text-size-adjust: 100%;
    tab-size: 4;
    line-height: 1.5;
    font-family: var(
      --default-font-family,
      ui-sans-serif,
      system-ui,
      sans-serif,
      'Apple Color Emoji',
      'Segoe UI Emoji',
      'Segoe UI Symbol',
      'Noto Color Emoji'
    );
    font-feature-settings: var(--default-font-feature-settings, normal);
    font-variation-settings: var(--default-font-variation-settings, normal);
    -webkit-tap-highlight-color: transparent;
  }
  hr {
    height: 0;
    color: inherit;
    border-top-width: 1px;
  }
  abbr:where([title]) {
    -webkit-text-decoration: underline dotted;
    text-decoration: underline dotted;
  }
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    font-size: inherit;
    font-weight: inherit;
  }
  a {
    color: inherit;
    -webkit-text-decoration: inherit;
    text-decoration: inherit;
  }
  b,
  strong {
    font-weight: bolder;
  }
  code,
  kbd,
  pre,
  samp {
    font-family: var(
      --default-mono-font-family,
      ui-monospace,
      SFMono-Regular,
      Menlo,
      Monaco,
      Consolas,
      'Liberation Mono',
      'Courier New',
      monospace
    );
    font-feature-settings: var(--default-mono-font-feature-settings, normal);
    font-variation-settings: var(
      --default-mono-font-variation-settings,
      normal
    );
    font-size: 1em;
  }
  small {
    font-size: 80%;
  }
  sub,
  sup {
    vertical-align: baseline;
    font-size: 75%;
    line-height: 0;
    position: relative;
  }
  sub {
    bottom: -0.25em;
  }
  sup {
    top: -0.5em;
  }
  table {
    text-indent: 0;
    border-color: inherit;
    border-collapse: collapse;
  }
  :-moz-focusring {
    outline: auto;
  }
  progress {
    vertical-align: baseline;
  }
  summary {
    display: list-item;
  }
  menu,
  ol,
  ul {
    list-style: none;
  }
  audio,
  canvas,
  embed,
  iframe,
  img,
  object,
  svg,
  video {
    vertical-align: middle;
    display: block;
  }
  img,
  video {
    max-width: 100%;
    height: auto;
  }
  button,
  input,
  optgroup,
  select,
  textarea {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    opacity: 1;
    background-color: #0000;
    border-radius: 0;
  }
  ::file-selector-button {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    opacity: 1;
    background-color: #0000;
    border-radius: 0;
  }
  :where(select:is([multiple], [size])) optgroup {
    font-weight: bolder;
  }
  :where(select:is([multiple], [size])) optgroup option {
    padding-inline-start: 20px;
  }
  ::file-selector-button {
    margin-inline-end: 4px;
  }
  ::placeholder {
    opacity: 1;
  }
  @supports (not ((-webkit-appearance: -apple-pay-button))) or
    (contain-intrinsic-size: 1px) {
    ::placeholder {
      color: currentColor;
    }
    @supports (color: color-mix(in lab, red, red)) {
      ::placeholder {
        color: color-mix(in oklab, currentcolor 50%, transparent);
      }
    }
  }
  textarea {
    resize: vertical;
  }
  ::-webkit-search-decoration {
    -webkit-appearance: none;
  }
  ::-webkit-date-and-time-value {
    min-height: 1lh;
    text-align: inherit;
  }
  ::-webkit-datetime-edit {
    display: inline-flex;
  }
  ::-webkit-datetime-edit-fields-wrapper {
    padding: 0;
  }
  ::-webkit-datetime-edit,
  ::-webkit-datetime-edit-year-field {
    padding-block: 0;
  }
  ::-webkit-datetime-edit-day-field,
  ::-webkit-datetime-edit-month-field {
    padding-block: 0;
  }
  ::-webkit-datetime-edit-hour-field,
  ::-webkit-datetime-edit-minute-field {
    padding-block: 0;
  }
  ::-webkit-datetime-edit-millisecond-field,
  ::-webkit-datetime-edit-second-field {
    padding-block: 0;
  }
  ::-webkit-datetime-edit-meridiem-field {
    padding-block: 0;
  }
  :-moz-ui-invalid {
    box-shadow: none;
  }
  button,
  input:where([type='button'], [type='reset'], [type='submit']) {
    appearance: button;
  }
  ::file-selector-button {
    appearance: button;
  }
  ::-webkit-inner-spin-button,
  ::-webkit-outer-spin-button {
    height: auto;
  }
  [hidden]:where(:not([hidden='until-found'])) {
    display: none !important;
  }
}
@layer components;
@layer utilities {
  .row-start-2 {
    grid-row-start: 2;
  }
  .row-start-3 {
    grid-row-start: 3;
  }
  .mb-2 {
    margin-bottom: calc(var(--spacing) * 2);
  }
  .flex {
    display: flex;
  }
  .grid {
    display: grid;
  }
  .h-10 {
    height: calc(var(--spacing) * 10);
  }
  .min-h-screen {
    min-height: 100vh;
  }
  .w-full {
    width: 100%;
  }
  .list-inside {
    list-style-position: inside;
  }
  .list-decimal {
    list-style-type: decimal;
  }
  .grid-rows-\[20px_1fr_20px\] {
    grid-template-rows: 20px 1fr 20px;
  }
  .flex-col {
    flex-direction: column;
  }
  .flex-wrap {
    flex-wrap: wrap;
  }
  .items-center {
    align-items: center;
  }
  .justify-center {
    justify-content: center;
  }
  .justify-items-center {
    justify-items: center;
  }
  .gap-2 {
    gap: calc(var(--spacing) * 2);
  }
  .gap-4 {
    gap: calc(var(--spacing) * 4);
  }
  .gap-16 {
    gap: calc(var(--spacing) * 16);
  }
  .gap-\[24px\] {
    gap: 24px;
  }
  .gap-\[32px\] {
    gap: 32px;
  }
  .rounded {
    border-radius: 0.25rem;
  }
  .rounded-full {
    border-radius: 3.40282e38px;
  }
  .border {
    border-style: var(--tw-border-style);
    border-width: 1px;
  }
  .border-solid {
    --tw-border-style: solid;
    border-style: solid;
  }
  .border-black\/\[\.08\] {
    border-color: #00000014;
  }
  @supports (color: color-mix(in lab, red, red)) {
    .border-black\/\[\.08\] {
      border-color: color-mix(in oklab, var(--color-black) 8%, transparent);
    }
  }
  .border-transparent {
    border-color: #0000;
  }
  .bg-black\/\[\.05\] {
    background-color: #0000000d;
  }
  @supports (color: color-mix(in lab, red, red)) {
    .bg-black\/\[\.05\] {
      background-color: color-mix(in oklab, var(--color-black) 5%, transparent);
    }
  }
  .bg-foreground {
    background-color: var(--foreground);
  }
  .p-8 {
    padding: calc(var(--spacing) * 8);
  }
  .px-1 {
    padding-inline: calc(var(--spacing) * 1);
  }
  .px-4 {
    padding-inline: calc(var(--spacing) * 4);
  }
  .py-0\.5 {
    padding-block: calc(var(--spacing) * 0.5);
  }
  .pb-20 {
    padding-bottom: calc(var(--spacing) * 20);
  }
  .text-center {
    text-align: center;
  }
  .font-mono {
    font-family: var(--font-geist-mono);
  }
  .font-sans {
    font-family: var(--font-geist-sans);
  }
  .text-sm {
    line-height: var(--tw-leading, var(--text-sm--line-height));
  }
  .text-sm,
  .text-sm\/6 {
    font-size: var(--text-sm);
  }
  .text-sm\/6 {
    line-height: calc(var(--spacing) * 6);
  }
  .font-medium {
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
  }
  .font-semibold {
    --tw-font-weight: var(--font-weight-semibold);
    font-weight: var(--font-weight-semibold);
  }
  .tracking-\[-\.01em\] {
    --tw-tracking: -0.01em;
    letter-spacing: -0.01em;
  }
  .text-background {
    color: var(--background);
  }
  .antialiased {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
  .transition-colors {
    transition-property:
      color, background-color, border-color, outline-color,
      text-decoration-color, fill, stroke, --tw-gradient-from,
      --tw-gradient-via, --tw-gradient-to;
    transition-timing-function: var(
      --tw-ease,
      var(--default-transition-timing-function)
    );
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  @media (hover: hover) {
    .hover\:border-transparent:hover {
      border-color: #0000;
    }
    .hover\:bg-\[\#383838\]:hover {
      background-color: #383838;
    }
    .hover\:bg-\[\#f2f2f2\]:hover {
      background-color: #f2f2f2;
    }
    .hover\:underline:hover {
      text-decoration-line: underline;
    }
    .hover\:underline-offset-4:hover {
      text-underline-offset: 4px;
    }
  }
  @media (min-width: 40rem) {
    .sm\:h-12 {
      height: calc(var(--spacing) * 12);
    }
    .sm\:w-auto {
      width: auto;
    }
    .sm\:flex-row {
      flex-direction: row;
    }
    .sm\:items-start {
      align-items: flex-start;
    }
    .sm\:p-20 {
      padding: calc(var(--spacing) * 20);
    }
    .sm\:px-5 {
      padding-inline: calc(var(--spacing) * 5);
    }
    .sm\:text-left {
      text-align: left;
    }
    .sm\:text-base {
      font-size: var(--text-base);
      line-height: var(--tw-leading, var(--text-base--line-height));
    }
  }
  @media (min-width: 48rem) {
    .md\:w-\[158px\] {
      width: 158px;
    }
  }
  @media (prefers-color-scheme: dark) {
    .dark\:border-white\/\[\.145\] {
      border-color: #ffffff25;
    }
    @supports (color: color-mix(in lab, red, red)) {
      .dark\:border-white\/\[\.145\] {
        border-color: color-mix(
          in oklab,
          var(--color-white) 14.5%,
          transparent
        );
      }
    }
    .dark\:bg-white\/\[\.06\] {
      background-color: #ffffff0f;
    }
    @supports (color: color-mix(in lab, red, red)) {
      .dark\:bg-white\/\[\.06\] {
        background-color: color-mix(
          in oklab,
          var(--color-white) 6%,
          transparent
        );
      }
    }
    .dark\:invert {
      --tw-invert: invert(100%);
      filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,)
        var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,)
        var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
    }
    @media (hover: hover) {
      .dark\:hover\:bg-\[\#1a1a1a\]:hover {
        background-color: #1a1a1a;
      }
      .dark\:hover\:bg-\[\#ccc\]:hover {
        background-color: #ccc;
      }
    }
  }
}
:root {
  --background: #fff;
  --foreground: #171717;
}
@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}
body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}
@property --tw-border-style {
  syntax: '*';
  inherits: false;
  initial-value: solid;
}
@property --tw-font-weight {
  syntax: '*';
  inherits: false;
}
@property --tw-tracking {
  syntax: '*';
  inherits: false;
}
@property --tw-blur {
  syntax: '*';
  inherits: false;
}
@property --tw-brightness {
  syntax: '*';
  inherits: false;
}
@property --tw-contrast {
  syntax: '*';
  inherits: false;
}
@property --tw-grayscale {
  syntax: '*';
  inherits: false;
}
@property --tw-hue-rotate {
  syntax: '*';
  inherits: false;
}
@property --tw-invert {
  syntax: '*';
  inherits: false;
}
@property --tw-opacity {
  syntax: '*';
  inherits: false;
}
@property --tw-saturate {
  syntax: '*';
  inherits: false;
}
@property --tw-sepia {
  syntax: '*';
  inherits: false;
}
@property --tw-drop-shadow {
  syntax: '*';
  inherits: false;
}
@property --tw-drop-shadow-color {
  syntax: '*';
  inherits: false;
}
@property --tw-drop-shadow-alpha {
  syntax: '<percentage>';
  inherits: false;
  initial-value: 100%;
}
@property --tw-drop-shadow-size {
  syntax: '*';
  inherits: false;
}
