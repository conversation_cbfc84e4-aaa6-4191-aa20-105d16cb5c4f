;(() => {
  var a = {}
  ;((a.id = 974),
    (a.ids = [974]),
    (a.modules = {
      261: (a) => {
        'use strict'
        a.exports = require('next/dist/shared/lib/router/utils/app-paths')
      },
      440: (a, b, c) => {
        'use strict'
        ;(c.r(b), c.d(b, { default: () => e }))
        var d = c(1658)
        let e = async (a) => [
          {
            type: 'image/x-icon',
            sizes: '16x16',
            url:
              (0, d.fillMetadataSegment)('.', await a.params, 'favicon.ico') +
              ''
          }
        ]
      },
      512: (a, b, c) => {
        'use strict'
        ;(Object.defineProperty(b, '__esModule', { value: !0 }),
          !(function (a, b) {
            for (var c in b)
              Object.defineProperty(a, c, { enumerable: !0, get: b[c] })
          })(b, {
            default: function () {
              return p
            },
            defaultHead: function () {
              return l
            }
          }))
        let d = c(4985),
          e = c(740),
          f = c(687),
          g = e._(c(3210)),
          h = d._(c(7755)),
          i = c(4959),
          j = c(9513),
          k = c(4604)
        function l(a) {
          void 0 === a && (a = !1)
          let b = [(0, f.jsx)('meta', { charSet: 'utf-8' }, 'charset')]
          return (
            a ||
              b.push(
                (0, f.jsx)(
                  'meta',
                  { name: 'viewport', content: 'width=device-width' },
                  'viewport'
                )
              ),
            b
          )
        }
        function m(a, b) {
          return 'string' == typeof b || 'number' == typeof b
            ? a
            : b.type === g.default.Fragment
              ? a.concat(
                  g.default.Children.toArray(b.props.children).reduce(
                    (a, b) =>
                      'string' == typeof b || 'number' == typeof b
                        ? a
                        : a.concat(b),
                    []
                  )
                )
              : a.concat(b)
        }
        c(148)
        let n = ['name', 'httpEquiv', 'charSet', 'itemProp']
        function o(a, b) {
          let { inAmpMode: c } = b
          return a
            .reduce(m, [])
            .reverse()
            .concat(l(c).reverse())
            .filter(
              (function () {
                let a = new Set(),
                  b = new Set(),
                  c = new Set(),
                  d = {}
                return (e) => {
                  let f = !0,
                    g = !1
                  if (
                    e.key &&
                    'number' != typeof e.key &&
                    e.key.indexOf('$') > 0
                  ) {
                    g = !0
                    let b = e.key.slice(e.key.indexOf('$') + 1)
                    a.has(b) ? (f = !1) : a.add(b)
                  }
                  switch (e.type) {
                    case 'title':
                    case 'base':
                      b.has(e.type) ? (f = !1) : b.add(e.type)
                      break
                    case 'meta':
                      for (let a = 0, b = n.length; a < b; a++) {
                        let b = n[a]
                        if (e.props.hasOwnProperty(b))
                          if ('charSet' === b) c.has(b) ? (f = !1) : c.add(b)
                          else {
                            let a = e.props[b],
                              c = d[b] || new Set()
                            ;('name' !== b || !g) && c.has(a)
                              ? (f = !1)
                              : (c.add(a), (d[b] = c))
                          }
                      }
                  }
                  return f
                }
              })()
            )
            .reverse()
            .map((a, b) => {
              let c = a.key || b
              return g.default.cloneElement(a, { key: c })
            })
        }
        let p = function (a) {
          let { children: b } = a,
            c = (0, g.useContext)(i.AmpStateContext),
            d = (0, g.useContext)(j.HeadManagerContext)
          return (0, f.jsx)(h.default, {
            reduceComponentsToState: o,
            headManager: d,
            inAmpMode: (0, k.isInAmpMode)(c),
            children: b
          })
        }
        ;('function' == typeof b.default ||
          ('object' == typeof b.default && null !== b.default)) &&
          void 0 === b.default.__esModule &&
          (Object.defineProperty(b.default, '__esModule', { value: !0 }),
          Object.assign(b.default, b),
          (a.exports = b.default))
      },
      846: (a) => {
        'use strict'
        a.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')
      },
      1025: (a) => {
        'use strict'
        a.exports = require('next/dist/server/app-render/dynamic-access-async-storage.external.js')
      },
      1122: (a, b) => {
        'use strict'
        ;(Object.defineProperty(b, '__esModule', { value: !0 }),
          Object.defineProperty(b, 'warnOnce', {
            enumerable: !0,
            get: function () {
              return c
            }
          }))
        let c = (a) => {}
      },
      1135: () => {},
      1322: (a, b) => {
        'use strict'
        function c(a) {
          let {
              widthInt: b,
              heightInt: c,
              blurWidth: d,
              blurHeight: e,
              blurDataURL: f,
              objectFit: g
            } = a,
            h = d ? 40 * d : b,
            i = e ? 40 * e : c,
            j = h && i ? "viewBox='0 0 " + h + ' ' + i + "'" : ''
          return (
            "%3Csvg xmlns='http://www.w3.org/2000/svg' " +
            j +
            "%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='" +
            (j
              ? 'none'
              : 'contain' === g
                ? 'xMidYMid'
                : 'cover' === g
                  ? 'xMidYMid slice'
                  : 'none') +
            "' style='filter: url(%23b);' href='" +
            f +
            "'/%3E%3C/svg%3E"
          )
        }
        ;(Object.defineProperty(b, '__esModule', { value: !0 }),
          Object.defineProperty(b, 'getImageBlurSvg', {
            enumerable: !0,
            get: function () {
              return c
            }
          }))
      },
      1480: (a, b) => {
        'use strict'
        function c(a) {
          let {
              widthInt: b,
              heightInt: c,
              blurWidth: d,
              blurHeight: e,
              blurDataURL: f,
              objectFit: g
            } = a,
            h = d ? 40 * d : b,
            i = e ? 40 * e : c,
            j = h && i ? "viewBox='0 0 " + h + ' ' + i + "'" : ''
          return (
            "%3Csvg xmlns='http://www.w3.org/2000/svg' " +
            j +
            "%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='" +
            (j
              ? 'none'
              : 'contain' === g
                ? 'xMidYMid'
                : 'cover' === g
                  ? 'xMidYMid slice'
                  : 'none') +
            "' style='filter: url(%23b);' href='" +
            f +
            "'/%3E%3C/svg%3E"
          )
        }
        ;(Object.defineProperty(b, '__esModule', { value: !0 }),
          Object.defineProperty(b, 'getImageBlurSvg', {
            enumerable: !0,
            get: function () {
              return c
            }
          }))
      },
      1602: (a, b, c) => {
        'use strict'
        ;(c.r(b),
          c.d(b, {
            GlobalError: () => C.a,
            __next_app__: () => I,
            handler: () => K,
            pages: () => H,
            routeModule: () => J,
            tree: () => G
          }))
        var d = c(5239),
          e = c(8088),
          f = c(7220),
          g = c(1289),
          h = c(6191),
          i = c(4823),
          j = c(1998),
          k = c(2603),
          l = c(4649),
          m = c(2781),
          n = c(2602),
          o = c(1268),
          p = c(4853),
          q = c(261),
          r = c(5052),
          s = c(9977),
          t = c(6713),
          u = c(3365),
          v = c(1454),
          w = c(7778),
          x = c(6143),
          y = c(9105),
          z = c(8171),
          A = c(6439),
          B = c(6133),
          C = c.n(B),
          D = c(893),
          E = c(2836),
          F = {}
        for (let a in D)
          0 >
            [
              'default',
              'tree',
              'pages',
              'GlobalError',
              '__next_app__',
              'routeModule',
              'handler'
            ].indexOf(a) && (F[a] = () => D[a])
        c.d(b, F)
        let G = {
            children: [
              '',
              {
                children: [
                  '__PAGE__',
                  {},
                  {
                    page: [
                      () => Promise.resolve().then(c.bind(c, 4500)),
                      'E:\\Code\\Portfolio\\NewMRH\\frontend\\src\\app\\page.tsx'
                    ],
                    metadata: {
                      icon: [
                        async (a) =>
                          (
                            await Promise.resolve().then(c.bind(c, 440))
                          ).default(a)
                      ],
                      apple: [],
                      openGraph: [],
                      twitter: [],
                      manifest: void 0
                    }
                  }
                ]
              },
              {
                layout: [
                  () => Promise.resolve().then(c.bind(c, 4431)),
                  'E:\\Code\\Portfolio\\NewMRH\\frontend\\src\\app\\layout.tsx'
                ],
                'global-error': [
                  () => Promise.resolve().then(c.t.bind(c, 6133, 23)),
                  'next/dist/client/components/builtin/global-error.js'
                ],
                'not-found': [
                  () => Promise.resolve().then(c.t.bind(c, 849, 23)),
                  'next/dist/client/components/builtin/not-found.js'
                ],
                forbidden: [
                  () => Promise.resolve().then(c.t.bind(c, 9868, 23)),
                  'next/dist/client/components/builtin/forbidden.js'
                ],
                unauthorized: [
                  () => Promise.resolve().then(c.t.bind(c, 9615, 23)),
                  'next/dist/client/components/builtin/unauthorized.js'
                ],
                metadata: {
                  icon: [
                    async (a) =>
                      (await Promise.resolve().then(c.bind(c, 440))).default(a)
                  ],
                  apple: [],
                  openGraph: [],
                  twitter: [],
                  manifest: void 0
                }
              }
            ]
          }.children,
          H = ['E:\\Code\\Portfolio\\NewMRH\\frontend\\src\\app\\page.tsx'],
          I = { require: c, loadChunk: () => Promise.resolve() },
          J = new d.AppPageRouteModule({
            definition: {
              kind: e.RouteKind.APP_PAGE,
              page: '/page',
              pathname: '/',
              bundlePath: '',
              filename: '',
              appPaths: []
            },
            userland: { loaderTree: G },
            distDir: '.next',
            projectDir: ''
          })
        async function K(a, b, c) {
          var d
          let B = '/page'
          '/index' === B && (B = '/')
          let F = 'false',
            L = (0, h.getRequestMeta)(a, 'postponed'),
            M = (0, h.getRequestMeta)(a, 'minimalMode'),
            N = await J.prepare(a, b, { srcPage: B, multiZoneDraftMode: F })
          if (!N)
            return (
              (b.statusCode = 400),
              b.end('Bad Request'),
              null == c.waitUntil || c.waitUntil.call(c, Promise.resolve()),
              null
            )
          let {
              buildId: O,
              query: P,
              params: Q,
              parsedUrl: R,
              pageIsDynamic: S,
              buildManifest: T,
              nextFontManifest: U,
              reactLoadableManifest: V,
              serverActionsManifest: W,
              clientReferenceManifest: X,
              subresourceIntegrityManifest: Y,
              prerenderManifest: Z,
              isDraftMode: $,
              resolvedPathname: _,
              revalidateOnlyGenerated: aa,
              routerServerContext: ab,
              nextConfig: ac
            } = N,
            ad = R.pathname || '/',
            ae = (0, q.normalizeAppPath)(B),
            { isOnDemandRevalidate: af } = N,
            ag = Z.dynamicRoutes[ae],
            ah = Z.routes[_],
            ai = !!(ag || ah || Z.routes[ae]),
            aj = a.headers['user-agent'] || '',
            ak = (0, t.getBotType)(aj),
            al = (0, o.isHtmlBotRequest)(a),
            am =
              (0, h.getRequestMeta)(a, 'isPrefetchRSCRequest') ??
              !!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],
            an =
              (0, h.getRequestMeta)(a, 'isRSCRequest') ??
              !!a.headers[s.RSC_HEADER],
            ao = (0, r.getIsPossibleServerAction)(a),
            ap =
              (0, l.checkIsAppPPREnabled)(ac.experimental.ppr) &&
              (null == (d = Z.routes[ae] ?? Z.dynamicRoutes[ae])
                ? void 0
                : d.renderingMode) === 'PARTIALLY_STATIC',
            aq = !1,
            ar = !1,
            as = ap ? L : void 0,
            at = ap && an && !am,
            au = (0, h.getRequestMeta)(a, 'segmentPrefetchRSCRequest'),
            av =
              !aj || (0, o.shouldServeStreamingMetadata)(aj, ac.htmlLimitedBots)
          al && ap && ((ai = !1), (av = !1))
          let aw = !0 === J.isDev || !ai || 'string' == typeof L || at,
            ax = al && ap,
            ay = null
          $ || !ai || aw || ao || as || at || (ay = _)
          let az = ay
          !az && J.isDev && (az = _)
          let aA = {
            ...D,
            tree: G,
            pages: H,
            GlobalError: C(),
            handler: K,
            routeModule: J,
            __next_app__: I
          }
          W &&
            X &&
            (0, n.setReferenceManifestsSingleton)({
              page: B,
              clientReferenceManifest: X,
              serverActionsManifest: W,
              serverModuleMap: (0, p.createServerModuleMap)({
                serverActionsManifest: W
              })
            })
          let aB = a.method || 'GET',
            aC = (0, g.getTracer)(),
            aD = aC.getActiveScopeSpan()
          try {
            let d = async (c, d) => {
                let e = new k.NodeNextRequest(a),
                  f = new k.NodeNextResponse(b)
                return J.render(e, f, d).finally(() => {
                  if (!c) return
                  c.setAttributes({
                    'http.status_code': b.statusCode,
                    'next.rsc': !1
                  })
                  let d = aC.getRootSpanAttributes()
                  if (!d) return
                  if (
                    d.get('next.span_type') !== i.BaseServerSpan.handleRequest
                  )
                    return void console.warn(
                      `Unexpected root span type '${d.get('next.span_type')}'. Please report this Next.js issue https://github.com/vercel/next.js`
                    )
                  let e = d.get('next.route')
                  if (e) {
                    let a = `${aB} ${e}`
                    ;(c.setAttributes({
                      'next.route': e,
                      'http.route': e,
                      'next.span_name': a
                    }),
                      c.updateName(a))
                  } else c.updateName(`${aB} ${a.url}`)
                })
              },
              f = async ({ span: e, postponed: f, fallbackRouteParams: g }) => {
                let i = {
                    query: P,
                    params: Q,
                    page: ae,
                    sharedContext: { buildId: O },
                    serverComponentsHmrCache: (0, h.getRequestMeta)(
                      a,
                      'serverComponentsHmrCache'
                    ),
                    fallbackRouteParams: g,
                    renderOpts: {
                      App: () => null,
                      Document: () => null,
                      pageConfig: {},
                      ComponentMod: aA,
                      Component: (0, j.T)(aA),
                      params: Q,
                      routeModule: J,
                      page: B,
                      postponed: f,
                      shouldWaitOnAllReady: ax,
                      serveStreamingMetadata: av,
                      supportsDynamicResponse: 'string' == typeof f || aw,
                      buildManifest: T,
                      nextFontManifest: U,
                      reactLoadableManifest: V,
                      subresourceIntegrityManifest: Y,
                      serverActionsManifest: W,
                      clientReferenceManifest: X,
                      setIsrStatus: null == ab ? void 0 : ab.setIsrStatus,
                      dir: J.projectDir,
                      isDraftMode: $,
                      isRevalidate: ai && !f && !at,
                      botType: ak,
                      isOnDemandRevalidate: af,
                      isPossibleServerAction: ao,
                      assetPrefix: ac.assetPrefix,
                      nextConfigOutput: ac.output,
                      crossOrigin: ac.crossOrigin,
                      trailingSlash: ac.trailingSlash,
                      previewProps: Z.preview,
                      deploymentId: ac.deploymentId,
                      enableTainting: ac.experimental.taint,
                      htmlLimitedBots: ac.htmlLimitedBots,
                      devtoolSegmentExplorer:
                        ac.experimental.devtoolSegmentExplorer,
                      reactMaxHeadersLength: ac.reactMaxHeadersLength,
                      multiZoneDraftMode: F,
                      incrementalCache: (0, h.getRequestMeta)(
                        a,
                        'incrementalCache'
                      ),
                      cacheLifeProfiles: ac.experimental.cacheLife,
                      basePath: ac.basePath,
                      serverActions: ac.experimental.serverActions,
                      ...(aq
                        ? {
                            nextExport: !0,
                            supportsDynamicResponse: !1,
                            isStaticGeneration: !0,
                            isRevalidate: !0,
                            isDebugDynamicAccesses: aq
                          }
                        : {}),
                      experimental: {
                        isRoutePPREnabled: ap,
                        expireTime: ac.expireTime,
                        staleTimes: ac.experimental.staleTimes,
                        dynamicIO: !!ac.experimental.dynamicIO,
                        clientSegmentCache:
                          !!ac.experimental.clientSegmentCache,
                        dynamicOnHover: !!ac.experimental.dynamicOnHover,
                        inlineCss: !!ac.experimental.inlineCss,
                        authInterrupts: !!ac.experimental.authInterrupts,
                        clientTraceMetadata:
                          ac.experimental.clientTraceMetadata || []
                      },
                      waitUntil: c.waitUntil,
                      onClose: (a) => {
                        b.on('close', a)
                      },
                      onAfterTaskError: () => {},
                      onInstrumentationRequestError: (b, c, d) =>
                        J.onRequestError(a, b, d, ab),
                      err: (0, h.getRequestMeta)(a, 'invokeError'),
                      dev: J.isDev
                    }
                  },
                  k = await d(e, i),
                  { metadata: l } = k,
                  { cacheControl: m, headers: n = {}, fetchTags: o } = l
                if (
                  (o && (n[x.NEXT_CACHE_TAGS_HEADER] = o),
                  (a.fetchMetrics = l.fetchMetrics),
                  ai &&
                    (null == m ? void 0 : m.revalidate) === 0 &&
                    !J.isDev &&
                    !ap)
                ) {
                  let a = l.staticBailoutInfo,
                    b = Object.defineProperty(
                      Error(`Page changed from static to dynamic at runtime ${_}${(null == a ? void 0 : a.description) ? `, reason: ${a.description}` : ''}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),
                      '__NEXT_ERROR_CODE',
                      { value: 'E132', enumerable: !1, configurable: !0 }
                    )
                  if (null == a ? void 0 : a.stack) {
                    let c = a.stack
                    b.stack = b.message + c.substring(c.indexOf('\n'))
                  }
                  throw b
                }
                return {
                  value: {
                    kind: u.CachedRouteKind.APP_PAGE,
                    html: k,
                    headers: n,
                    rscData: l.flightData,
                    postponed: l.postponed,
                    status: l.statusCode,
                    segmentData: l.segmentData
                  },
                  cacheControl: m
                }
              },
              l = async ({
                hasResolved: d,
                previousCacheEntry: g,
                isRevalidating: i,
                span: j
              }) => {
                let k,
                  l = !1 === J.isDev,
                  n = d || b.writableEnded
                if (af && aa && !g && !M)
                  return (
                    (null == ab ? void 0 : ab.render404)
                      ? await ab.render404(a, b)
                      : ((b.statusCode = 404),
                        b.end('This page could not be found')),
                    null
                  )
                if (
                  (ag && (k = (0, v.parseFallbackField)(ag.fallback)),
                  k === v.FallbackMode.PRERENDER &&
                    (0, t.isBot)(aj) &&
                    (k = v.FallbackMode.BLOCKING_STATIC_RENDER),
                  (null == g ? void 0 : g.isStale) === -1 && (af = !0),
                  af &&
                    (k !== v.FallbackMode.NOT_FOUND || g) &&
                    (k = v.FallbackMode.BLOCKING_STATIC_RENDER),
                  !M &&
                    k !== v.FallbackMode.BLOCKING_STATIC_RENDER &&
                    az &&
                    !n &&
                    !$ &&
                    S &&
                    (l || !ah))
                ) {
                  let b
                  if ((l || ag) && k === v.FallbackMode.NOT_FOUND)
                    throw new A.NoFallbackError()
                  if (ap && !an) {
                    if (
                      ((b = await J.handleResponse({
                        cacheKey: l ? ae : null,
                        req: a,
                        nextConfig: ac,
                        routeKind: e.RouteKind.APP_PAGE,
                        isFallback: !0,
                        prerenderManifest: Z,
                        isRoutePPREnabled: ap,
                        responseGenerator: async () =>
                          f({
                            span: j,
                            postponed: void 0,
                            fallbackRouteParams: l || ar ? (0, m.u)(ae) : null
                          }),
                        waitUntil: c.waitUntil
                      })),
                      null === b)
                    )
                      return null
                    if (b) return (delete b.cacheControl, b)
                  }
                }
                let o = af || i || !as ? void 0 : as
                if (aq && void 0 !== o)
                  return {
                    cacheControl: { revalidate: 1, expire: void 0 },
                    value: {
                      kind: u.CachedRouteKind.PAGES,
                      html: w.default.fromStatic(''),
                      pageData: {},
                      headers: void 0,
                      status: void 0
                    }
                  }
                let p =
                  S &&
                  ap &&
                  ((0, h.getRequestMeta)(a, 'renderFallbackShell') || ar)
                    ? (0, m.u)(ad)
                    : null
                return f({ span: j, postponed: o, fallbackRouteParams: p })
              },
              n = async (d) => {
                var g, i, j, k, m
                let n,
                  o = await J.handleResponse({
                    cacheKey: ay,
                    responseGenerator: (a) => l({ span: d, ...a }),
                    routeKind: e.RouteKind.APP_PAGE,
                    isOnDemandRevalidate: af,
                    isRoutePPREnabled: ap,
                    req: a,
                    nextConfig: ac,
                    prerenderManifest: Z,
                    waitUntil: c.waitUntil
                  })
                if (
                  ($ &&
                    b.setHeader(
                      'Cache-Control',
                      'private, no-cache, no-store, max-age=0, must-revalidate'
                    ),
                  J.isDev &&
                    b.setHeader('Cache-Control', 'no-store, must-revalidate'),
                  !o)
                ) {
                  if (ay)
                    throw Object.defineProperty(
                      Error(
                        'invariant: cache entry required but not generated'
                      ),
                      '__NEXT_ERROR_CODE',
                      { value: 'E62', enumerable: !1, configurable: !0 }
                    )
                  return null
                }
                if (
                  (null == (g = o.value) ? void 0 : g.kind) !==
                  u.CachedRouteKind.APP_PAGE
                )
                  throw Object.defineProperty(
                    Error(
                      `Invariant app-page handler received invalid cache entry ${null == (j = o.value) ? void 0 : j.kind}`
                    ),
                    '__NEXT_ERROR_CODE',
                    { value: 'E707', enumerable: !1, configurable: !0 }
                  )
                let p = 'string' == typeof o.value.postponed
                ai &&
                  !at &&
                  (!p || am) &&
                  (M ||
                    b.setHeader(
                      'x-nextjs-cache',
                      af
                        ? 'REVALIDATED'
                        : o.isMiss
                          ? 'MISS'
                          : o.isStale
                            ? 'STALE'
                            : 'HIT'
                    ),
                  b.setHeader(s.NEXT_IS_PRERENDER_HEADER, '1'))
                let { value: q } = o
                if (as) n = { revalidate: 0, expire: void 0 }
                else if (M && an && !am && ap)
                  n = { revalidate: 0, expire: void 0 }
                else if (!J.isDev)
                  if ($) n = { revalidate: 0, expire: void 0 }
                  else if (ai) {
                    if (o.cacheControl)
                      if ('number' == typeof o.cacheControl.revalidate) {
                        if (o.cacheControl.revalidate < 1)
                          throw Object.defineProperty(
                            Error(
                              `Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`
                            ),
                            '__NEXT_ERROR_CODE',
                            { value: 'E22', enumerable: !1, configurable: !0 }
                          )
                        n = {
                          revalidate: o.cacheControl.revalidate,
                          expire:
                            (null == (k = o.cacheControl)
                              ? void 0
                              : k.expire) ?? ac.expireTime
                        }
                      } else
                        n = { revalidate: x.CACHE_ONE_YEAR, expire: void 0 }
                  } else
                    b.getHeader('Cache-Control') ||
                      (n = { revalidate: 0, expire: void 0 })
                if (
                  ((o.cacheControl = n),
                  'string' == typeof au &&
                    (null == q ? void 0 : q.kind) ===
                      u.CachedRouteKind.APP_PAGE &&
                    q.segmentData)
                ) {
                  b.setHeader(s.NEXT_DID_POSTPONE_HEADER, '2')
                  let c =
                    null == (m = q.headers)
                      ? void 0
                      : m[x.NEXT_CACHE_TAGS_HEADER]
                  M &&
                    ai &&
                    c &&
                    'string' == typeof c &&
                    b.setHeader(x.NEXT_CACHE_TAGS_HEADER, c)
                  let d = q.segmentData.get(au)
                  return void 0 !== d
                    ? (0, z.sendRenderResult)({
                        req: a,
                        res: b,
                        type: 'rsc',
                        generateEtags: ac.generateEtags,
                        poweredByHeader: ac.poweredByHeader,
                        result: w.default.fromStatic(d),
                        cacheControl: o.cacheControl
                      })
                    : ((b.statusCode = 204),
                      (0, z.sendRenderResult)({
                        req: a,
                        res: b,
                        type: 'rsc',
                        generateEtags: ac.generateEtags,
                        poweredByHeader: ac.poweredByHeader,
                        result: w.default.fromStatic(''),
                        cacheControl: o.cacheControl
                      }))
                }
                let r = (0, h.getRequestMeta)(a, 'onCacheEntry')
                if (
                  r &&
                  (await r(
                    { ...o, value: { ...o.value, kind: 'PAGE' } },
                    { url: (0, h.getRequestMeta)(a, 'initURL') }
                  ))
                )
                  return null
                if (p && as)
                  throw Object.defineProperty(
                    Error(
                      'Invariant: postponed state should not be present on a resume request'
                    ),
                    '__NEXT_ERROR_CODE',
                    { value: 'E396', enumerable: !1, configurable: !0 }
                  )
                if (q.headers) {
                  let a = { ...q.headers }
                  for (let [c, d] of ((M && ai) ||
                    delete a[x.NEXT_CACHE_TAGS_HEADER],
                  Object.entries(a)))
                    if (void 0 !== d)
                      if (Array.isArray(d))
                        for (let a of d) b.appendHeader(c, a)
                      else
                        ('number' == typeof d && (d = d.toString()),
                          b.appendHeader(c, d))
                }
                let t =
                  null == (i = q.headers) ? void 0 : i[x.NEXT_CACHE_TAGS_HEADER]
                if (
                  (M &&
                    ai &&
                    t &&
                    'string' == typeof t &&
                    b.setHeader(x.NEXT_CACHE_TAGS_HEADER, t),
                  !q.status || (an && ap) || (b.statusCode = q.status),
                  !M &&
                    q.status &&
                    E.RedirectStatusCode[q.status] &&
                    an &&
                    (b.statusCode = 200),
                  p && b.setHeader(s.NEXT_DID_POSTPONE_HEADER, '1'),
                  an && !$)
                ) {
                  if (void 0 === q.rscData) {
                    if (q.postponed)
                      throw Object.defineProperty(
                        Error('Invariant: Expected postponed to be undefined'),
                        '__NEXT_ERROR_CODE',
                        { value: 'E372', enumerable: !1, configurable: !0 }
                      )
                    return (0, z.sendRenderResult)({
                      req: a,
                      res: b,
                      type: 'rsc',
                      generateEtags: ac.generateEtags,
                      poweredByHeader: ac.poweredByHeader,
                      result: q.html,
                      cacheControl: at
                        ? { revalidate: 0, expire: void 0 }
                        : o.cacheControl
                    })
                  }
                  return (0, z.sendRenderResult)({
                    req: a,
                    res: b,
                    type: 'rsc',
                    generateEtags: ac.generateEtags,
                    poweredByHeader: ac.poweredByHeader,
                    result: w.default.fromStatic(q.rscData),
                    cacheControl: o.cacheControl
                  })
                }
                let v = q.html
                if (!p || M)
                  return (0, z.sendRenderResult)({
                    req: a,
                    res: b,
                    type: 'html',
                    generateEtags: ac.generateEtags,
                    poweredByHeader: ac.poweredByHeader,
                    result: v,
                    cacheControl: o.cacheControl
                  })
                if (aq)
                  return (
                    v.chain(
                      new ReadableStream({
                        start(a) {
                          ;(a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),
                            a.close())
                        }
                      })
                    ),
                    (0, z.sendRenderResult)({
                      req: a,
                      res: b,
                      type: 'html',
                      generateEtags: ac.generateEtags,
                      poweredByHeader: ac.poweredByHeader,
                      result: v,
                      cacheControl: { revalidate: 0, expire: void 0 }
                    })
                  )
                let A = new TransformStream()
                return (
                  v.chain(A.readable),
                  f({
                    span: d,
                    postponed: q.postponed,
                    fallbackRouteParams: null
                  })
                    .then(async (a) => {
                      var b, c
                      if (!a)
                        throw Object.defineProperty(
                          Error('Invariant: expected a result to be returned'),
                          '__NEXT_ERROR_CODE',
                          { value: 'E463', enumerable: !1, configurable: !0 }
                        )
                      if (
                        (null == (b = a.value) ? void 0 : b.kind) !==
                        u.CachedRouteKind.APP_PAGE
                      )
                        throw Object.defineProperty(
                          Error(
                            `Invariant: expected a page response, got ${null == (c = a.value) ? void 0 : c.kind}`
                          ),
                          '__NEXT_ERROR_CODE',
                          { value: 'E305', enumerable: !1, configurable: !0 }
                        )
                      await a.value.html.pipeTo(A.writable)
                    })
                    .catch((a) => {
                      A.writable.abort(a).catch((a) => {
                        console.error("couldn't abort transformer", a)
                      })
                    }),
                  (0, z.sendRenderResult)({
                    req: a,
                    res: b,
                    type: 'html',
                    generateEtags: ac.generateEtags,
                    poweredByHeader: ac.poweredByHeader,
                    result: v,
                    cacheControl: { revalidate: 0, expire: void 0 }
                  })
                )
              }
            if (!aD)
              return await aC.withPropagatedContext(a.headers, () =>
                aC.trace(
                  i.BaseServerSpan.handleRequest,
                  {
                    spanName: `${aB} ${a.url}`,
                    kind: g.SpanKind.SERVER,
                    attributes: { 'http.method': aB, 'http.target': a.url }
                  },
                  n
                )
              )
            await n(aD)
          } catch (b) {
            throw (
              aD ||
                b instanceof A.NoFallbackError ||
                (await J.onRequestError(
                  a,
                  b,
                  {
                    routerKind: 'App Router',
                    routePath: B,
                    routeType: 'render',
                    revalidateReason: (0, f.c)({
                      isRevalidate: ai,
                      isOnDemandRevalidate: af
                    })
                  },
                  ab
                )),
              b
            )
          }
        }
      },
      1836: (a, b, c) => {
        Promise.resolve().then(c.t.bind(c, 6533, 23))
      },
      1933: (a, b) => {
        'use strict'
        function c(a) {
          var b
          let { config: c, src: d, width: e, quality: f } = a,
            g =
              f ||
              (null == (b = c.qualities)
                ? void 0
                : b.reduce((a, b) =>
                    Math.abs(b - 75) < Math.abs(a - 75) ? b : a
                  )) ||
              75
          return (
            c.path +
            '?url=' +
            encodeURIComponent(d) +
            '&w=' +
            e +
            '&q=' +
            g +
            (d.startsWith('/_next/static/media/'), '')
          )
        }
        ;(Object.defineProperty(b, '__esModule', { value: !0 }),
          Object.defineProperty(b, 'default', {
            enumerable: !0,
            get: function () {
              return d
            }
          }),
          (c.__next_img_default = !0))
        let d = c
      },
      1984: () => {},
      2091: (a, b) => {
        'use strict'
        function c(a) {
          var b
          let { config: c, src: d, width: e, quality: f } = a,
            g =
              f ||
              (null == (b = c.qualities)
                ? void 0
                : b.reduce((a, b) =>
                    Math.abs(b - 75) < Math.abs(a - 75) ? b : a
                  )) ||
              75
          return (
            c.path +
            '?url=' +
            encodeURIComponent(d) +
            '&w=' +
            e +
            '&q=' +
            g +
            (d.startsWith('/_next/static/media/'), '')
          )
        }
        ;(Object.defineProperty(b, '__esModule', { value: !0 }),
          Object.defineProperty(b, 'default', {
            enumerable: !0,
            get: function () {
              return d
            }
          }),
          (c.__next_img_default = !0))
        let d = c
      },
      2480: (a, b, c) => {
        'use strict'
        ;(Object.defineProperty(b, '__esModule', { value: !0 }),
          !(function (a, b) {
            for (var c in b)
              Object.defineProperty(a, c, { enumerable: !0, get: b[c] })
          })(b, {
            default: function () {
              return i
            },
            getImageProps: function () {
              return h
            }
          }))
        let d = c(2639),
          e = c(9131),
          f = c(9603),
          g = d._(c(2091))
        function h(a) {
          let { props: b } = (0, e.getImgProps)(a, {
            defaultLoader: g.default,
            imgConf: {
              deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
              imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
              path: '/_next/image',
              loader: 'default',
              dangerouslyAllowSVG: !1,
              unoptimized: !1
            }
          })
          for (let [a, c] of Object.entries(b)) void 0 === c && delete b[a]
          return { props: b }
        }
        let i = f.Image
      },
      2639: (a, b, c) => {
        'use strict'
        function d(a) {
          return a && a.__esModule ? a : { default: a }
        }
        ;(c.r(b), c.d(b, { _: () => d }))
      },
      2756: (a, b) => {
        'use strict'
        ;(Object.defineProperty(b, '__esModule', { value: !0 }),
          !(function (a, b) {
            for (var c in b)
              Object.defineProperty(a, c, { enumerable: !0, get: b[c] })
          })(b, {
            VALID_LOADERS: function () {
              return c
            },
            imageConfigDefault: function () {
              return d
            }
          }))
        let c = ['default', 'imgix', 'cloudinary', 'akamai', 'custom'],
          d = {
            deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
            imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
            path: '/_next/image',
            loader: 'default',
            loaderFile: '',
            domains: [],
            disableStaticImages: !1,
            minimumCacheTTL: 60,
            formats: ['image/webp'],
            dangerouslyAllowSVG: !1,
            contentSecurityPolicy:
              "script-src 'none'; frame-src 'none'; sandbox;",
            contentDispositionType: 'attachment',
            localPatterns: void 0,
            remotePatterns: [],
            qualities: void 0,
            unoptimized: !1
          }
      },
      3033: (a) => {
        'use strict'
        a.exports = require('next/dist/server/app-render/work-unit-async-storage.external.js')
      },
      3038: (a, b, c) => {
        'use strict'
        ;(Object.defineProperty(b, '__esModule', { value: !0 }),
          Object.defineProperty(b, 'useMergedRef', {
            enumerable: !0,
            get: function () {
              return e
            }
          }))
        let d = c(3210)
        function e(a, b) {
          let c = (0, d.useRef)(null),
            e = (0, d.useRef)(null)
          return (0, d.useCallback)(
            (d) => {
              if (null === d) {
                let a = c.current
                a && ((c.current = null), a())
                let b = e.current
                b && ((e.current = null), b())
              } else (a && (c.current = f(a, d)), b && (e.current = f(b, d)))
            },
            [a, b]
          )
        }
        function f(a, b) {
          if ('function' != typeof a)
            return (
              (a.current = b),
              () => {
                a.current = null
              }
            )
          {
            let c = a(b)
            return 'function' == typeof c ? c : () => a(null)
          }
        }
        ;('function' == typeof b.default ||
          ('object' == typeof b.default && null !== b.default)) &&
          void 0 === b.default.__esModule &&
          (Object.defineProperty(b.default, '__esModule', { value: !0 }),
          Object.assign(b.default, b),
          (a.exports = b.default))
      },
      3295: (a) => {
        'use strict'
        a.exports = require('next/dist/server/app-render/after-task-async-storage.external.js')
      },
      3873: (a) => {
        'use strict'
        a.exports = require('path')
      },
      4431: (a, b, c) => {
        'use strict'
        ;(c.r(b), c.d(b, { default: () => j, metadata: () => i }))
        var d = c(7413),
          e = c(2376),
          f = c.n(e),
          g = c(8726),
          h = c.n(g)
        c(1135)
        let i = {
          title: 'MRH Application',
          description: 'Multi-tenant real estate management application'
        }
        function j({ children: a }) {
          return (0, d.jsx)('html', {
            lang: 'en',
            children: (0, d.jsx)('body', {
              className: `${f().variable} ${h().variable} antialiased`,
              children: a
            })
          })
        }
      },
      4500: (a, b, c) => {
        'use strict'
        ;(c.r(b), c.d(b, { default: () => g }))
        var d = c(7413),
          e = c(2480),
          f = c.n(e)
        function g() {
          return (0, d.jsxs)('div', {
            className:
              'font-sans grid grid-rows-[20px_1fr_20px] items-center justify-items-center min-h-screen p-8 pb-20 gap-16 sm:p-20',
            children: [
              (0, d.jsxs)('main', {
                className:
                  'flex flex-col gap-[32px] row-start-2 items-center sm:items-start',
                children: [
                  (0, d.jsx)(f(), {
                    className: 'dark:invert',
                    src: '/next.svg',
                    alt: 'Next.js logo',
                    width: 180,
                    height: 38,
                    priority: !0
                  }),
                  (0, d.jsxs)('ol', {
                    className:
                      'font-mono list-inside list-decimal text-sm/6 text-center sm:text-left',
                    children: [
                      (0, d.jsxs)('li', {
                        className: 'mb-2 tracking-[-.01em]',
                        children: [
                          'Get started by editing',
                          ' ',
                          (0, d.jsx)('code', {
                            className:
                              'bg-black/[.05] dark:bg-white/[.06] font-mono font-semibold px-1 py-0.5 rounded',
                            children: 'src/app/page.tsx'
                          }),
                          '.'
                        ]
                      }),
                      (0, d.jsx)('li', {
                        className: 'tracking-[-.01em]',
                        children: 'Save and see your changes instantly.'
                      })
                    ]
                  }),
                  (0, d.jsxs)('div', {
                    className: 'flex gap-4 items-center flex-col sm:flex-row',
                    children: [
                      (0, d.jsxs)('a', {
                        className:
                          'rounded-full border border-solid border-transparent transition-colors flex items-center justify-center bg-foreground text-background gap-2 hover:bg-[#383838] dark:hover:bg-[#ccc] font-medium text-sm sm:text-base h-10 sm:h-12 px-4 sm:px-5 sm:w-auto',
                        href: 'https://vercel.com/new?utm_source=create-next-app&utm_medium=appdir-template-tw&utm_campaign=create-next-app',
                        target: '_blank',
                        rel: 'noopener noreferrer',
                        children: [
                          (0, d.jsx)(f(), {
                            className: 'dark:invert',
                            src: '/vercel.svg',
                            alt: 'Vercel logomark',
                            width: 20,
                            height: 20
                          }),
                          'Deploy now'
                        ]
                      }),
                      (0, d.jsx)('a', {
                        className:
                          'rounded-full border border-solid border-black/[.08] dark:border-white/[.145] transition-colors flex items-center justify-center hover:bg-[#f2f2f2] dark:hover:bg-[#1a1a1a] hover:border-transparent font-medium text-sm sm:text-base h-10 sm:h-12 px-4 sm:px-5 w-full sm:w-auto md:w-[158px]',
                        href: 'https://nextjs.org/docs?utm_source=create-next-app&utm_medium=appdir-template-tw&utm_campaign=create-next-app',
                        target: '_blank',
                        rel: 'noopener noreferrer',
                        children: 'Read our docs'
                      })
                    ]
                  })
                ]
              }),
              (0, d.jsxs)('footer', {
                className:
                  'row-start-3 flex gap-[24px] flex-wrap items-center justify-center',
                children: [
                  (0, d.jsxs)('a', {
                    className:
                      'flex items-center gap-2 hover:underline hover:underline-offset-4',
                    href: 'https://nextjs.org/learn?utm_source=create-next-app&utm_medium=appdir-template-tw&utm_campaign=create-next-app',
                    target: '_blank',
                    rel: 'noopener noreferrer',
                    children: [
                      (0, d.jsx)(f(), {
                        'aria-hidden': !0,
                        src: '/file.svg',
                        alt: 'File icon',
                        width: 16,
                        height: 16
                      }),
                      'Learn'
                    ]
                  }),
                  (0, d.jsxs)('a', {
                    className:
                      'flex items-center gap-2 hover:underline hover:underline-offset-4',
                    href: 'https://vercel.com/templates?framework=next.js&utm_source=create-next-app&utm_medium=appdir-template-tw&utm_campaign=create-next-app',
                    target: '_blank',
                    rel: 'noopener noreferrer',
                    children: [
                      (0, d.jsx)(f(), {
                        'aria-hidden': !0,
                        src: '/window.svg',
                        alt: 'Window icon',
                        width: 16,
                        height: 16
                      }),
                      'Examples'
                    ]
                  }),
                  (0, d.jsxs)('a', {
                    className:
                      'flex items-center gap-2 hover:underline hover:underline-offset-4',
                    href: 'https://nextjs.org?utm_source=create-next-app&utm_medium=appdir-template-tw&utm_campaign=create-next-app',
                    target: '_blank',
                    rel: 'noopener noreferrer',
                    children: [
                      (0, d.jsx)(f(), {
                        'aria-hidden': !0,
                        src: '/globe.svg',
                        alt: 'Globe icon',
                        width: 16,
                        height: 16
                      }),
                      'Go to nextjs.org →'
                    ]
                  })
                ]
              })
            ]
          })
        }
      },
      4604: (a, b) => {
        'use strict'
        function c(a) {
          let {
            ampFirst: b = !1,
            hybrid: c = !1,
            hasQuery: d = !1
          } = void 0 === a ? {} : a
          return b || (c && d)
        }
        ;(Object.defineProperty(b, '__esModule', { value: !0 }),
          Object.defineProperty(b, 'isInAmpMode', {
            enumerable: !0,
            get: function () {
              return c
            }
          }))
      },
      4953: (a, b, c) => {
        'use strict'
        ;(Object.defineProperty(b, '__esModule', { value: !0 }),
          Object.defineProperty(b, 'getImgProps', {
            enumerable: !0,
            get: function () {
              return i
            }
          }),
          c(148))
        let d = c(1480),
          e = c(2756),
          f = ['-moz-initial', 'fill', 'none', 'scale-down', void 0]
        function g(a) {
          return void 0 !== a.default
        }
        function h(a) {
          return void 0 === a
            ? a
            : 'number' == typeof a
              ? Number.isFinite(a)
                ? a
                : NaN
              : 'string' == typeof a && /^[0-9]+$/.test(a)
                ? parseInt(a, 10)
                : NaN
        }
        function i(a, b) {
          var c, i
          let j,
            k,
            l,
            {
              src: m,
              sizes: n,
              unoptimized: o = !1,
              priority: p = !1,
              loading: q,
              className: r,
              quality: s,
              width: t,
              height: u,
              fill: v = !1,
              style: w,
              overrideSrc: x,
              onLoad: y,
              onLoadingComplete: z,
              placeholder: A = 'empty',
              blurDataURL: B,
              fetchPriority: C,
              decoding: D = 'async',
              layout: E,
              objectFit: F,
              objectPosition: G,
              lazyBoundary: H,
              lazyRoot: I,
              ...J
            } = a,
            {
              imgConf: K,
              showAltText: L,
              blurComplete: M,
              defaultLoader: N
            } = b,
            O = K || e.imageConfigDefault
          if ('allSizes' in O) j = O
          else {
            let a = [...O.deviceSizes, ...O.imageSizes].sort((a, b) => a - b),
              b = O.deviceSizes.sort((a, b) => a - b),
              d = null == (c = O.qualities) ? void 0 : c.sort((a, b) => a - b)
            j = { ...O, allSizes: a, deviceSizes: b, qualities: d }
          }
          if (void 0 === N)
            throw Object.defineProperty(
              Error(
                'images.loaderFile detected but the file is missing default export.\nRead more: https://nextjs.org/docs/messages/invalid-images-config'
              ),
              '__NEXT_ERROR_CODE',
              { value: 'E163', enumerable: !1, configurable: !0 }
            )
          let P = J.loader || N
          ;(delete J.loader, delete J.srcSet)
          let Q = '__next_img_default' in P
          if (Q) {
            if ('custom' === j.loader)
              throw Object.defineProperty(
                Error(
                  'Image with src "' +
                    m +
                    '" is missing "loader" prop.\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader'
                ),
                '__NEXT_ERROR_CODE',
                { value: 'E252', enumerable: !1, configurable: !0 }
              )
          } else {
            let a = P
            P = (b) => {
              let { config: c, ...d } = b
              return a(d)
            }
          }
          if (E) {
            'fill' === E && (v = !0)
            let a = {
              intrinsic: { maxWidth: '100%', height: 'auto' },
              responsive: { width: '100%', height: 'auto' }
            }[E]
            a && (w = { ...w, ...a })
            let b = { responsive: '100vw', fill: '100vw' }[E]
            b && !n && (n = b)
          }
          let R = '',
            S = h(t),
            T = h(u)
          if ((i = m) && 'object' == typeof i && (g(i) || void 0 !== i.src)) {
            let a = g(m) ? m.default : m
            if (!a.src)
              throw Object.defineProperty(
                Error(
                  'An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received ' +
                    JSON.stringify(a)
                ),
                '__NEXT_ERROR_CODE',
                { value: 'E460', enumerable: !1, configurable: !0 }
              )
            if (!a.height || !a.width)
              throw Object.defineProperty(
                Error(
                  'An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received ' +
                    JSON.stringify(a)
                ),
                '__NEXT_ERROR_CODE',
                { value: 'E48', enumerable: !1, configurable: !0 }
              )
            if (
              ((k = a.blurWidth),
              (l = a.blurHeight),
              (B = B || a.blurDataURL),
              (R = a.src),
              !v)
            )
              if (S || T) {
                if (S && !T) {
                  let b = S / a.width
                  T = Math.round(a.height * b)
                } else if (!S && T) {
                  let b = T / a.height
                  S = Math.round(a.width * b)
                }
              } else ((S = a.width), (T = a.height))
          }
          let U = !p && ('lazy' === q || void 0 === q)
          ;((!(m = 'string' == typeof m ? m : R) ||
            m.startsWith('data:') ||
            m.startsWith('blob:')) &&
            ((o = !0), (U = !1)),
            j.unoptimized && (o = !0),
            Q &&
              !j.dangerouslyAllowSVG &&
              m.split('?', 1)[0].endsWith('.svg') &&
              (o = !0))
          let V = h(s),
            W = Object.assign(
              v
                ? {
                    position: 'absolute',
                    height: '100%',
                    width: '100%',
                    left: 0,
                    top: 0,
                    right: 0,
                    bottom: 0,
                    objectFit: F,
                    objectPosition: G
                  }
                : {},
              L ? {} : { color: 'transparent' },
              w
            ),
            X =
              M || 'empty' === A
                ? null
                : 'blur' === A
                  ? 'url("data:image/svg+xml;charset=utf-8,' +
                    (0, d.getImageBlurSvg)({
                      widthInt: S,
                      heightInt: T,
                      blurWidth: k,
                      blurHeight: l,
                      blurDataURL: B || '',
                      objectFit: W.objectFit
                    }) +
                    '")'
                  : 'url("' + A + '")',
            Y = f.includes(W.objectFit)
              ? 'fill' === W.objectFit
                ? '100% 100%'
                : 'cover'
              : W.objectFit,
            Z = X
              ? {
                  backgroundSize: Y,
                  backgroundPosition: W.objectPosition || '50% 50%',
                  backgroundRepeat: 'no-repeat',
                  backgroundImage: X
                }
              : {},
            $ = (function (a) {
              let {
                config: b,
                src: c,
                unoptimized: d,
                width: e,
                quality: f,
                sizes: g,
                loader: h
              } = a
              if (d) return { src: c, srcSet: void 0, sizes: void 0 }
              let { widths: i, kind: j } = (function (a, b, c) {
                  let { deviceSizes: d, allSizes: e } = a
                  if (c) {
                    let a = /(^|\s)(1?\d?\d)vw/g,
                      b = []
                    for (let d; (d = a.exec(c)); ) b.push(parseInt(d[2]))
                    if (b.length) {
                      let a = 0.01 * Math.min(...b)
                      return {
                        widths: e.filter((b) => b >= d[0] * a),
                        kind: 'w'
                      }
                    }
                    return { widths: e, kind: 'w' }
                  }
                  return 'number' != typeof b
                    ? { widths: d, kind: 'w' }
                    : {
                        widths: [
                          ...new Set(
                            [b, 2 * b].map(
                              (a) => e.find((b) => b >= a) || e[e.length - 1]
                            )
                          )
                        ],
                        kind: 'x'
                      }
                })(b, e, g),
                k = i.length - 1
              return {
                sizes: g || 'w' !== j ? g : '100vw',
                srcSet: i
                  .map(
                    (a, d) =>
                      h({ config: b, src: c, quality: f, width: a }) +
                      ' ' +
                      ('w' === j ? a : d + 1) +
                      j
                  )
                  .join(', '),
                src: h({ config: b, src: c, quality: f, width: i[k] })
              }
            })({
              config: j,
              src: m,
              unoptimized: o,
              width: S,
              quality: V,
              sizes: n,
              loader: P
            })
          return {
            props: {
              ...J,
              loading: U ? 'lazy' : q,
              fetchPriority: C,
              width: S,
              height: T,
              decoding: D,
              className: r,
              style: { ...W, ...Z },
              sizes: $.sizes,
              srcSet: $.srcSet,
              src: x || $.src
            },
            meta: { unoptimized: o, priority: p, placeholder: A, fill: v }
          }
        }
      },
      4959: (a, b, c) => {
        'use strict'
        a.exports = c(4041).vendored.contexts.AmpContext
      },
      5348: (a, b, c) => {
        ;(Promise.resolve().then(c.t.bind(c, 5227, 23)),
          Promise.resolve().then(c.t.bind(c, 6346, 23)),
          Promise.resolve().then(c.t.bind(c, 7924, 23)),
          Promise.resolve().then(c.t.bind(c, 99, 23)),
          Promise.resolve().then(c.t.bind(c, 8243, 23)),
          Promise.resolve().then(c.t.bind(c, 8827, 23)),
          Promise.resolve().then(c.t.bind(c, 2763, 23)),
          Promise.resolve().then(c.t.bind(c, 7173, 23)),
          Promise.resolve().then(c.bind(c, 5587)))
      },
      5536: () => {},
      6439: (a) => {
        'use strict'
        a.exports = require('next/dist/shared/lib/no-fallback-error.external')
      },
      6533: (a, b, c) => {
        'use strict'
        ;(Object.defineProperty(b, '__esModule', { value: !0 }),
          Object.defineProperty(b, 'Image', {
            enumerable: !0,
            get: function () {
              return u
            }
          }))
        let d = c(4985),
          e = c(740),
          f = c(687),
          g = e._(c(3210)),
          h = d._(c(1215)),
          i = d._(c(512)),
          j = c(4953),
          k = c(2756),
          l = c(7903)
        c(148)
        let m = c(9148),
          n = d._(c(1933)),
          o = c(3038),
          p = {
            deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
            imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
            path: '/_next/image',
            loader: 'default',
            dangerouslyAllowSVG: !1,
            unoptimized: !1
          }
        function q(a, b, c, d, e, f, g) {
          let h = null == a ? void 0 : a.src
          a &&
            a['data-loaded-src'] !== h &&
            ((a['data-loaded-src'] = h),
            ('decode' in a ? a.decode() : Promise.resolve())
              .catch(() => {})
              .then(() => {
                if (a.parentElement && a.isConnected) {
                  if (
                    ('empty' !== b && e(!0), null == c ? void 0 : c.current)
                  ) {
                    let b = new Event('load')
                    Object.defineProperty(b, 'target', {
                      writable: !1,
                      value: a
                    })
                    let d = !1,
                      e = !1
                    c.current({
                      ...b,
                      nativeEvent: b,
                      currentTarget: a,
                      target: a,
                      isDefaultPrevented: () => d,
                      isPropagationStopped: () => e,
                      persist: () => {},
                      preventDefault: () => {
                        ;((d = !0), b.preventDefault())
                      },
                      stopPropagation: () => {
                        ;((e = !0), b.stopPropagation())
                      }
                    })
                  }
                  ;(null == d ? void 0 : d.current) && d.current(a)
                }
              }))
        }
        function r(a) {
          return g.use ? { fetchPriority: a } : { fetchpriority: a }
        }
        globalThis.__NEXT_IMAGE_IMPORTED = !0
        let s = (0, g.forwardRef)((a, b) => {
          let {
              src: c,
              srcSet: d,
              sizes: e,
              height: h,
              width: i,
              decoding: j,
              className: k,
              style: l,
              fetchPriority: m,
              placeholder: n,
              loading: p,
              unoptimized: s,
              fill: t,
              onLoadRef: u,
              onLoadingCompleteRef: v,
              setBlurComplete: w,
              setShowAltText: x,
              sizesInput: y,
              onLoad: z,
              onError: A,
              ...B
            } = a,
            C = (0, g.useCallback)(
              (a) => {
                a &&
                  (A && (a.src = a.src), a.complete && q(a, n, u, v, w, s, y))
              },
              [c, n, u, v, w, A, s, y]
            ),
            D = (0, o.useMergedRef)(b, C)
          return (0, f.jsx)('img', {
            ...B,
            ...r(m),
            loading: p,
            width: i,
            height: h,
            decoding: j,
            'data-nimg': t ? 'fill' : '1',
            className: k,
            style: l,
            sizes: e,
            srcSet: d,
            src: c,
            ref: D,
            onLoad: (a) => {
              q(a.currentTarget, n, u, v, w, s, y)
            },
            onError: (a) => {
              ;(x(!0), 'empty' !== n && w(!0), A && A(a))
            }
          })
        })
        function t(a) {
          let { isAppRouter: b, imgAttributes: c } = a,
            d = {
              as: 'image',
              imageSrcSet: c.srcSet,
              imageSizes: c.sizes,
              crossOrigin: c.crossOrigin,
              referrerPolicy: c.referrerPolicy,
              ...r(c.fetchPriority)
            }
          return b && h.default.preload
            ? (h.default.preload(c.src, d), null)
            : (0, f.jsx)(i.default, {
                children: (0, f.jsx)(
                  'link',
                  { rel: 'preload', href: c.srcSet ? void 0 : c.src, ...d },
                  '__nimg-' + c.src + c.srcSet + c.sizes
                )
              })
        }
        let u = (0, g.forwardRef)((a, b) => {
          let c = (0, g.useContext)(m.RouterContext),
            d = (0, g.useContext)(l.ImageConfigContext),
            e = (0, g.useMemo)(() => {
              var a
              let b = p || d || k.imageConfigDefault,
                c = [...b.deviceSizes, ...b.imageSizes].sort((a, b) => a - b),
                e = b.deviceSizes.sort((a, b) => a - b),
                f = null == (a = b.qualities) ? void 0 : a.sort((a, b) => a - b)
              return { ...b, allSizes: c, deviceSizes: e, qualities: f }
            }, [d]),
            { onLoad: h, onLoadingComplete: i } = a,
            o = (0, g.useRef)(h)
          ;(0, g.useEffect)(() => {
            o.current = h
          }, [h])
          let q = (0, g.useRef)(i)
          ;(0, g.useEffect)(() => {
            q.current = i
          }, [i])
          let [r, u] = (0, g.useState)(!1),
            [v, w] = (0, g.useState)(!1),
            { props: x, meta: y } = (0, j.getImgProps)(a, {
              defaultLoader: n.default,
              imgConf: e,
              blurComplete: r,
              showAltText: v
            })
          return (0, f.jsxs)(f.Fragment, {
            children: [
              (0, f.jsx)(s, {
                ...x,
                unoptimized: y.unoptimized,
                placeholder: y.placeholder,
                fill: y.fill,
                onLoadRef: o,
                onLoadingCompleteRef: q,
                setBlurComplete: u,
                setShowAltText: w,
                sizesInput: a.sizes,
                ref: b
              }),
              y.priority
                ? (0, f.jsx)(t, { isAppRouter: !c, imgAttributes: x })
                : null
            ]
          })
        })
        ;('function' == typeof b.default ||
          ('object' == typeof b.default && null !== b.default)) &&
          void 0 === b.default.__esModule &&
          (Object.defineProperty(b.default, '__esModule', { value: !0 }),
          Object.assign(b.default, b),
          (a.exports = b.default))
      },
      6713: (a) => {
        'use strict'
        a.exports = require('next/dist/shared/lib/router/utils/is-bot')
      },
      7755: (a, b, c) => {
        'use strict'
        ;(Object.defineProperty(b, '__esModule', { value: !0 }),
          Object.defineProperty(b, 'default', {
            enumerable: !0,
            get: function () {
              return f
            }
          }))
        let d = c(3210),
          e = () => {}
        function f(a) {
          var b
          let { headManager: c, reduceComponentsToState: f } = a
          function g() {
            if (c && c.mountedInstances) {
              let b = d.Children.toArray(
                Array.from(c.mountedInstances).filter(Boolean)
              )
              c.updateHead(f(b, a))
            }
          }
          return (
            null == c || null == (b = c.mountedInstances) || b.add(a.children),
            g(),
            e(() => {
              var b
              return (
                null == c ||
                  null == (b = c.mountedInstances) ||
                  b.add(a.children),
                () => {
                  var b
                  null == c ||
                    null == (b = c.mountedInstances) ||
                    b.delete(a.children)
                }
              )
            }),
            e(
              () => (
                c && (c._pendingUpdate = g),
                () => {
                  c && (c._pendingUpdate = g)
                }
              )
            ),
            null
          )
        }
      },
      7894: (a, b) => {
        'use strict'
        ;(Object.defineProperty(b, '__esModule', { value: !0 }),
          !(function (a, b) {
            for (var c in b)
              Object.defineProperty(a, c, { enumerable: !0, get: b[c] })
          })(b, {
            VALID_LOADERS: function () {
              return c
            },
            imageConfigDefault: function () {
              return d
            }
          }))
        let c = ['default', 'imgix', 'cloudinary', 'akamai', 'custom'],
          d = {
            deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
            imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
            path: '/_next/image',
            loader: 'default',
            loaderFile: '',
            domains: [],
            disableStaticImages: !1,
            minimumCacheTTL: 60,
            formats: ['image/webp'],
            dangerouslyAllowSVG: !1,
            contentSecurityPolicy:
              "script-src 'none'; frame-src 'none'; sandbox;",
            contentDispositionType: 'attachment',
            localPatterns: void 0,
            remotePatterns: [],
            qualities: void 0,
            unoptimized: !1
          }
      },
      7903: (a, b, c) => {
        'use strict'
        a.exports = c(4041).vendored.contexts.ImageConfigContext
      },
      8284: (a, b, c) => {
        Promise.resolve().then(c.t.bind(c, 9603, 23))
      },
      8354: (a) => {
        'use strict'
        a.exports = require('util')
      },
      9121: (a) => {
        'use strict'
        a.exports = require('next/dist/server/app-render/action-async-storage.external.js')
      },
      9131: (a, b, c) => {
        'use strict'
        ;(Object.defineProperty(b, '__esModule', { value: !0 }),
          Object.defineProperty(b, 'getImgProps', {
            enumerable: !0,
            get: function () {
              return i
            }
          }),
          c(1122))
        let d = c(1322),
          e = c(7894),
          f = ['-moz-initial', 'fill', 'none', 'scale-down', void 0]
        function g(a) {
          return void 0 !== a.default
        }
        function h(a) {
          return void 0 === a
            ? a
            : 'number' == typeof a
              ? Number.isFinite(a)
                ? a
                : NaN
              : 'string' == typeof a && /^[0-9]+$/.test(a)
                ? parseInt(a, 10)
                : NaN
        }
        function i(a, b) {
          var c, i
          let j,
            k,
            l,
            {
              src: m,
              sizes: n,
              unoptimized: o = !1,
              priority: p = !1,
              loading: q,
              className: r,
              quality: s,
              width: t,
              height: u,
              fill: v = !1,
              style: w,
              overrideSrc: x,
              onLoad: y,
              onLoadingComplete: z,
              placeholder: A = 'empty',
              blurDataURL: B,
              fetchPriority: C,
              decoding: D = 'async',
              layout: E,
              objectFit: F,
              objectPosition: G,
              lazyBoundary: H,
              lazyRoot: I,
              ...J
            } = a,
            {
              imgConf: K,
              showAltText: L,
              blurComplete: M,
              defaultLoader: N
            } = b,
            O = K || e.imageConfigDefault
          if ('allSizes' in O) j = O
          else {
            let a = [...O.deviceSizes, ...O.imageSizes].sort((a, b) => a - b),
              b = O.deviceSizes.sort((a, b) => a - b),
              d = null == (c = O.qualities) ? void 0 : c.sort((a, b) => a - b)
            j = { ...O, allSizes: a, deviceSizes: b, qualities: d }
          }
          if (void 0 === N)
            throw Object.defineProperty(
              Error(
                'images.loaderFile detected but the file is missing default export.\nRead more: https://nextjs.org/docs/messages/invalid-images-config'
              ),
              '__NEXT_ERROR_CODE',
              { value: 'E163', enumerable: !1, configurable: !0 }
            )
          let P = J.loader || N
          ;(delete J.loader, delete J.srcSet)
          let Q = '__next_img_default' in P
          if (Q) {
            if ('custom' === j.loader)
              throw Object.defineProperty(
                Error(
                  'Image with src "' +
                    m +
                    '" is missing "loader" prop.\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader'
                ),
                '__NEXT_ERROR_CODE',
                { value: 'E252', enumerable: !1, configurable: !0 }
              )
          } else {
            let a = P
            P = (b) => {
              let { config: c, ...d } = b
              return a(d)
            }
          }
          if (E) {
            'fill' === E && (v = !0)
            let a = {
              intrinsic: { maxWidth: '100%', height: 'auto' },
              responsive: { width: '100%', height: 'auto' }
            }[E]
            a && (w = { ...w, ...a })
            let b = { responsive: '100vw', fill: '100vw' }[E]
            b && !n && (n = b)
          }
          let R = '',
            S = h(t),
            T = h(u)
          if ((i = m) && 'object' == typeof i && (g(i) || void 0 !== i.src)) {
            let a = g(m) ? m.default : m
            if (!a.src)
              throw Object.defineProperty(
                Error(
                  'An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received ' +
                    JSON.stringify(a)
                ),
                '__NEXT_ERROR_CODE',
                { value: 'E460', enumerable: !1, configurable: !0 }
              )
            if (!a.height || !a.width)
              throw Object.defineProperty(
                Error(
                  'An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received ' +
                    JSON.stringify(a)
                ),
                '__NEXT_ERROR_CODE',
                { value: 'E48', enumerable: !1, configurable: !0 }
              )
            if (
              ((k = a.blurWidth),
              (l = a.blurHeight),
              (B = B || a.blurDataURL),
              (R = a.src),
              !v)
            )
              if (S || T) {
                if (S && !T) {
                  let b = S / a.width
                  T = Math.round(a.height * b)
                } else if (!S && T) {
                  let b = T / a.height
                  S = Math.round(a.width * b)
                }
              } else ((S = a.width), (T = a.height))
          }
          let U = !p && ('lazy' === q || void 0 === q)
          ;((!(m = 'string' == typeof m ? m : R) ||
            m.startsWith('data:') ||
            m.startsWith('blob:')) &&
            ((o = !0), (U = !1)),
            j.unoptimized && (o = !0),
            Q &&
              !j.dangerouslyAllowSVG &&
              m.split('?', 1)[0].endsWith('.svg') &&
              (o = !0))
          let V = h(s),
            W = Object.assign(
              v
                ? {
                    position: 'absolute',
                    height: '100%',
                    width: '100%',
                    left: 0,
                    top: 0,
                    right: 0,
                    bottom: 0,
                    objectFit: F,
                    objectPosition: G
                  }
                : {},
              L ? {} : { color: 'transparent' },
              w
            ),
            X =
              M || 'empty' === A
                ? null
                : 'blur' === A
                  ? 'url("data:image/svg+xml;charset=utf-8,' +
                    (0, d.getImageBlurSvg)({
                      widthInt: S,
                      heightInt: T,
                      blurWidth: k,
                      blurHeight: l,
                      blurDataURL: B || '',
                      objectFit: W.objectFit
                    }) +
                    '")'
                  : 'url("' + A + '")',
            Y = f.includes(W.objectFit)
              ? 'fill' === W.objectFit
                ? '100% 100%'
                : 'cover'
              : W.objectFit,
            Z = X
              ? {
                  backgroundSize: Y,
                  backgroundPosition: W.objectPosition || '50% 50%',
                  backgroundRepeat: 'no-repeat',
                  backgroundImage: X
                }
              : {},
            $ = (function (a) {
              let {
                config: b,
                src: c,
                unoptimized: d,
                width: e,
                quality: f,
                sizes: g,
                loader: h
              } = a
              if (d) return { src: c, srcSet: void 0, sizes: void 0 }
              let { widths: i, kind: j } = (function (a, b, c) {
                  let { deviceSizes: d, allSizes: e } = a
                  if (c) {
                    let a = /(^|\s)(1?\d?\d)vw/g,
                      b = []
                    for (let d; (d = a.exec(c)); ) b.push(parseInt(d[2]))
                    if (b.length) {
                      let a = 0.01 * Math.min(...b)
                      return {
                        widths: e.filter((b) => b >= d[0] * a),
                        kind: 'w'
                      }
                    }
                    return { widths: e, kind: 'w' }
                  }
                  return 'number' != typeof b
                    ? { widths: d, kind: 'w' }
                    : {
                        widths: [
                          ...new Set(
                            [b, 2 * b].map(
                              (a) => e.find((b) => b >= a) || e[e.length - 1]
                            )
                          )
                        ],
                        kind: 'x'
                      }
                })(b, e, g),
                k = i.length - 1
              return {
                sizes: g || 'w' !== j ? g : '100vw',
                srcSet: i
                  .map(
                    (a, d) =>
                      h({ config: b, src: c, quality: f, width: a }) +
                      ' ' +
                      ('w' === j ? a : d + 1) +
                      j
                  )
                  .join(', '),
                src: h({ config: b, src: c, quality: f, width: i[k] })
              }
            })({
              config: j,
              src: m,
              unoptimized: o,
              width: S,
              quality: V,
              sizes: n,
              loader: P
            })
          return {
            props: {
              ...J,
              loading: U ? 'lazy' : q,
              fetchPriority: C,
              width: S,
              height: T,
              decoding: D,
              className: r,
              style: { ...W, ...Z },
              sizes: $.sizes,
              srcSet: $.srcSet,
              src: x || $.src
            },
            meta: { unoptimized: o, priority: p, placeholder: A, fill: v }
          }
        }
      },
      9148: (a, b, c) => {
        'use strict'
        a.exports = c(4041).vendored.contexts.RouterContext
      },
      9294: (a) => {
        'use strict'
        a.exports = require('next/dist/server/app-render/work-async-storage.external.js')
      },
      9316: (a, b, c) => {
        ;(Promise.resolve().then(c.t.bind(c, 6133, 23)),
          Promise.resolve().then(c.t.bind(c, 6444, 23)),
          Promise.resolve().then(c.t.bind(c, 6042, 23)),
          Promise.resolve().then(c.t.bind(c, 9477, 23)),
          Promise.resolve().then(c.t.bind(c, 9345, 23)),
          Promise.resolve().then(c.t.bind(c, 2089, 23)),
          Promise.resolve().then(c.t.bind(c, 6577, 23)),
          Promise.resolve().then(c.t.bind(c, 1307, 23)),
          Promise.resolve().then(c.t.bind(c, 4817, 23)))
      },
      9513: (a, b, c) => {
        'use strict'
        a.exports = c(4041).vendored.contexts.HeadManagerContext
      },
      9603: (a, b, c) => {
        let { createProxy: d } = c(9844)
        a.exports = d(
          'E:\\Code\\Portfolio\\NewMRH\\frontend\\node_modules\\next\\dist\\client\\image-component.js'
        )
      }
    }))
  var b = require('../webpack-runtime.js')
  b.C(a)
  var c = b.X(0, [985, 400], () => b((b.s = 1602)))
  module.exports = c
})()
