'use strict'
var __decorate =
	(this && this.__decorate) ||
	function (decorators, target, key, desc) {
		var c = arguments.length,
			r =
				c < 3
					? target
					: desc === null
						? (desc = Object.getOwnPropertyDescriptor(target, key))
						: desc,
			d
		if (typeof Reflect === 'object' && typeof Reflect.decorate === 'function')
			r = Reflect.decorate(decorators, target, key, desc)
		else
			for (var i = decorators.length - 1; i >= 0; i--)
				if ((d = decorators[i]))
					r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r
		return (c > 3 && r && Object.defineProperty(target, key, r), r)
	}
var __metadata =
	(this && this.__metadata) ||
	function (k, v) {
		if (typeof Reflect === 'object' && typeof Reflect.metadata === 'function')
			return Reflect.metadata(k, v)
	}
Object.defineProperty(exports, '__esModule', { value: true })
exports.CreateListingDto = void 0
const class_validator_1 = require('class-validator')
const class_transformer_1 = require('class-transformer')
class CreateListingDto {
	orgId
	title
	price
}
exports.CreateListingDto = CreateListingDto
__decorate(
	[
		(0, class_validator_1.IsString)(),
		(0, class_validator_1.MinLength)(1, { message: 'orgId cannot be empty' }),
		__metadata('design:type', String)
	],
	CreateListingDto.prototype,
	'orgId',
	void 0
)
__decorate(
	[
		(0, class_validator_1.IsString)(),
		(0, class_validator_1.MinLength)(3, {
			message: 'title must be at least 3 characters long'
		}),
		__metadata('design:type', String)
	],
	CreateListingDto.prototype,
	'title',
	void 0
)
__decorate(
	[
		(0, class_validator_1.IsNumber)({}, { message: 'price must be a number' }),
		(0, class_validator_1.IsPositive)({ message: 'price must be positive' }),
		(0, class_transformer_1.Type)(() => Number),
		(0, class_transformer_1.Transform)(({ value }) => parseInt(value)),
		__metadata('design:type', Number)
	],
	CreateListingDto.prototype,
	'price',
	void 0
)
//# sourceMappingURL=create-listing.dto.js.map
