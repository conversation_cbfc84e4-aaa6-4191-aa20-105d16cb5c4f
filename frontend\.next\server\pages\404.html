<!doctype html>
<!--JYXMq3egPC6voWYK9Xffi-->
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <link
      rel="stylesheet"
      href="/_next/static/css/a413225de6777efb.css"
      data-precedence="next"
    />
    <link
      rel="preload"
      as="script"
      fetchpriority="low"
      href="/_next/static/chunks/webpack-f0934ce8f449c9d7.js"
    />
    <script
      src="/_next/static/chunks/4bd1b696-cf72ae8a39fa05aa.js"
      async=""
    ></script>
    <script
      src="/_next/static/chunks/964-02efbd2195ef91bd.js"
      async=""
    ></script>
    <script
      src="/_next/static/chunks/main-app-9f85d8aad017cdbc.js"
      async=""
    ></script>
    <meta name="robots" content="noindex" />
    <title>404: This page could not be found.</title>
    <title>MRH Application</title>
    <meta
      name="description"
      content="Multi-tenant real estate management application"
    />
    <link rel="icon" href="/favicon.ico" type="image/x-icon" sizes="16x16" />
    <script
      src="/_next/static/chunks/polyfills-42372ed130431b0a.js"
      nomodule=""
    ></script>
  </head>
  <body class="__variable_5cfdac __variable_9a8899 antialiased">
    <div hidden=""><!--$--><!--/$--></div>
    <div
      style="
        font-family:
          system-ui, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif,
          'Apple Color Emoji', 'Segoe UI Emoji';
        height: 100vh;
        text-align: center;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
      "
    >
      <div>
        <style>
          body {
            color: #000;
            background: #fff;
            margin: 0;
          }
          .next-error-h1 {
            border-right: 1px solid rgba(0, 0, 0, 0.3);
          }
          @media (prefers-color-scheme: dark) {
            body {
              color: #fff;
              background: #000;
            }
            .next-error-h1 {
              border-right: 1px solid rgba(255, 255, 255, 0.3);
            }
          }
        </style>
        <h1
          class="next-error-h1"
          style="
            display: inline-block;
            margin: 0 20px 0 0;
            padding: 0 23px 0 0;
            font-size: 24px;
            font-weight: 500;
            vertical-align: top;
            line-height: 49px;
          "
        >
          404
        </h1>
        <div style="display: inline-block">
          <h2
            style="
              font-size: 14px;
              font-weight: 400;
              line-height: 49px;
              margin: 0;
            "
          >
            This page could not be found.
          </h2>
        </div>
      </div>
    </div>
    <!--$--><!--/$-->
    <script
      src="/_next/static/chunks/webpack-f0934ce8f449c9d7.js"
      id="_R_"
      async=""
    ></script>
    <script>
      ;(self.__next_f = self.__next_f || []).push([0])
    </script>
    <script>
      self.__next_f.push([
        1,
        '1:"$Sreact.fragment"\n2:I[7555,[],""]\n3:I[1295,[],""]\n4:I[9665,[],"OutletBoundary"]\n6:I[4911,[],"AsyncMetadataOutlet"]\n8:I[9665,[],"ViewportBoundary"]\na:I[9665,[],"MetadataBoundary"]\nb:"$Sreact.suspense"\nd:I[8393,[],""]\n:HL["/_next/static/css/a413225de6777efb.css","style"]\n'
      ])
    </script>
    <script>
      self.__next_f.push([
        1,
        '0:{"P":null,"b":"JYXMq3egPC6voWYK9Xffi","p":"","c":["","_not-found"],"i":false,"f":[[["",{"children":["/_not-found",{"children":["__PAGE__",{}]}]},"$undefined","$undefined",true],["",["$","$1","c",{"children":[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/a413225de6777efb.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}]],["$","html",null,{"lang":"en","children":["$","body",null,{"className":"__variable_5cfdac __variable_9a8899 antialiased","children":["$","$L2",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L3",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","forbidden":"$undefined","unauthorized":"$undefined"}]}]}]]}],{"children":["/_not-found",["$","$1","c",{"children":[null,["$","$L2",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L3",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","forbidden":"$undefined","unauthorized":"$undefined"}]]}],{"children":["__PAGE__",["$","$1","c",{"children":[[["$","title",null,{"children":"404: This page could not be found."}],["$","div",null,{"style":{"fontFamily":"system-ui,\\"Segoe UI\\",Roboto,Helvetica,Arial,sans-serif,\\"Apple Color Emoji\\",\\"Segoe UI Emoji\\"","height":"100vh","textAlign":"center","display":"flex","flexDirection":"column","alignItems":"center","justifyContent":"center"},"children":["$","div",null,{"children":[["$","style",null,{"dangerouslySetInnerHTML":{"__html":"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"}}],["$","h1",null,{"className":"next-error-h1","style":{"display":"inline-block","margin":"0 20px 0 0","padding":"0 23px 0 0","fontSize":24,"fontWeight":500,"verticalAlign":"top","lineHeight":"49px"},"children":404}],["$","div",null,{"style":{"display":"inline-block"},"children":["$","h2",null,{"style":{"fontSize":14,"fontWeight":400,"lineHeight":"49px","margin":0},"children":"This page could not be found."}]}]]}]}]],null,["$","$L4",null,{"children":["$L5",["$","$L6",null,{"promise":"$@7"}]]}]]}],{},null,false]},null,false]},null,false],["$","$1","h",{"children":[["$","meta",null,{"name":"robots","content":"noindex"}],[["$","$L8",null,{"children":"$L9"}],null],["$","$La",null,{"children":["$","div",null,{"hidden":true,"children":["$","$b",null,{"fallback":null,"children":"$Lc"}]}]}]]}],false]],"m":"$undefined","G":["$d",[]],"s":false,"S":true}\n'
      ])
    </script>
    <script>
      self.__next_f.push([
        1,
        '9:[["$","meta","0",{"charSet":"utf-8"}],["$","meta","1",{"name":"viewport","content":"width=device-width, initial-scale=1"}]]\n5:null\n'
      ])
    </script>
    <script>
      self.__next_f.push([
        1,
        'e:I[8175,[],"IconMark"]\n7:{"metadata":[["$","title","0",{"children":"MRH Application"}],["$","meta","1",{"name":"description","content":"Multi-tenant real estate management application"}],["$","link","2",{"rel":"icon","href":"/favicon.ico","type":"image/x-icon","sizes":"16x16"}],["$","$Le","3",{}]],"error":null,"digest":"$undefined"}\n'
      ])
    </script>
    <script>
      self.__next_f.push([1, 'c:"$7:metadata"\n'])
    </script>
  </body>
</html>
