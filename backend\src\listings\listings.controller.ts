import { Body, Controller, Post, UseGuards, Request } from '@nestjs/common'
import { makeCreateListing, Subject } from '@mrh/shared'
import { PrismaService } from '../prisma/prisma.service'
import { JwtAuthGuard } from '../auth/jwt-auth.guard'
import { PolicyGuard } from '../auth/policy.guard'
import { CreateListingDto } from './dto/create-listing.dto'

@Controller('listings')
export class ListingsController {
	constructor(private readonly prisma: PrismaService) {}

	@Post()
	@UseGuards(JwtAuthGuard, PolicyGuard)
	async create(
		@Body() dto: CreateListingDto,
		@Request() req: { user: Subject }
	) {
		const user: Subject = req.user // Set by JwtAuthGuard
		return await makeCreateListing({ prisma: this.prisma })(user, dto)
	}
}
