'use strict'
Object.defineProperty(exports, '__esModule', { value: true })
const core_1 = require('@nestjs/core')
const common_1 = require('@nestjs/common')
const app_module_1 = require('./app.module')
async function bootstrap() {
	const app = await core_1.NestFactory.create(app_module_1.AppModule)
	const logger = new common_1.Logger('Bootstrap')
	app.useGlobalPipes(
		new common_1.ValidationPipe({
			whitelist: true,
			forbidNonWhitelisted: true,
			transform: true
		})
	)
	app.enableCors({
		origin: process.env.FRONTEND_URL || 'http://localhost:3000',
		credentials: true
	})
	const port = process.env.PORT ?? 3001
	await app.listen(port)
	logger.log(`Application is running on: http://localhost:${port}`)
}
bootstrap().catch(error => {
	console.error('Failed to start application:', error)
	process.exit(1)
})
//# sourceMappingURL=main.js.map
