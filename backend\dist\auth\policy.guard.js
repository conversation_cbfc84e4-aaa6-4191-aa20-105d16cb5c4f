'use strict'
var __decorate =
	(this && this.__decorate) ||
	function (decorators, target, key, desc) {
		var c = arguments.length,
			r =
				c < 3
					? target
					: desc === null
						? (desc = Object.getOwnPropertyDescriptor(target, key))
						: desc,
			d
		if (typeof Reflect === 'object' && typeof Reflect.decorate === 'function')
			r = Reflect.decorate(decorators, target, key, desc)
		else
			for (var i = decorators.length - 1; i >= 0; i--)
				if ((d = decorators[i]))
					r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r
		return (c > 3 && r && Object.defineProperty(target, key, r), r)
	}
Object.defineProperty(exports, '__esModule', { value: true })
exports.PolicyGuard = void 0
const common_1 = require('@nestjs/common')
const shared_1 = require('@mrh/shared')
let PolicyGuard = class PolicyGuard {
	canActivate(ctx) {
		const req = ctx.switchToHttp().getRequest()
		const user = req.user
		const { orgId, ownerId } = req.body ?? {}
		;(0, shared_1.authorize)(user, 'listing:create', {
			kind: 'listing',
			orgId,
			ownerId
		})
		return true
	}
}
exports.PolicyGuard = PolicyGuard
exports.PolicyGuard = PolicyGuard = __decorate(
	[(0, common_1.Injectable)()],
	PolicyGuard
)
//# sourceMappingURL=policy.guard.js.map
